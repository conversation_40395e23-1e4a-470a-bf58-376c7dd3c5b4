import dotenv from 'dotenv';
dotenv.config();

import { SlashCommandBuilder } from '@discordjs/builders';
import { createClient } from '@supabase/supabase-js';
import { EmbedBuilder, PermissionsBitField } from 'discord.js';

const supabase = createClient(process.env.SUPABASE_PROJECT_URL, process.env.SUPABASE_ADMIN_KEY);

export const data = new SlashCommandBuilder()
  .setName('unban')
  .setDescription('🦌')
  .addUserOption((option) => option.setName('user').setDescription('🦌').setRequired(true));

export async function run({ interaction, client, handler }) {
  if (!interaction.member.permissions.has(PermissionsBitField.Flags.BanMembers)) {
    await interaction.reply({ content: 'Nie masz uprawnień do korzystania z tej komendy.', ephemeral: true });
    return;
  }

  const user = interaction.options.getUser('user');

  try {
    // Check if the user is already banned
    const { data: existingBan, error: checkError } = await supabase.from('dl_bans').select('user_id').eq('user_id', user.id);

    if (checkError) throw checkError;

    if (existingBan && existingBan.length < 0) {
      await interaction.reply({
        content: `User ${user.tag} is not banned from downlaod command.`,
        ephemeral: true,
      });
      return;
    }

    const { BanError } = await supabase.from('dl_bans').delete().eq('user_id', user.id);

    if (BanError) throw BanError;

    // Create an embed for the ban message
    const banEmbed = new EmbedBuilder()
      .setColor(0x00FF00)
      .setTitle('User Unbanned from Download Command')
      .addFields(
        { name: 'Unbanned User', value: `<@${user.id}> - ${user.tag} (${user.id})` },
        { name: 'Unbanned By', value: `<@${interaction.user.id}> - ${interaction.user.tag} (${interaction.user.id})` }
      )
      .setTimestamp();

    // Send the embed to the specified channel
    const logChannel = await client.channels.fetch('1260705584357969982');
    await logChannel.send({ embeds: [banEmbed] });

    // Reply to the interaction
    await interaction.reply({
      content: `User ${user.tag} has been unbanned from using the download command.`,
      ephemeral: true,
    });
  } catch (error) {
    console.error(`Error unbanning user from download command: ${error}`);
    await interaction.reply({
      content: 'An error occurred while banning the user from the download command.',
      ephemeral: true,
    });
  }
}

export const options = {
  devOnly: true,
  deleted: false,
};
