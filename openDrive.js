import axios from 'axios';
import process from 'process';
import FormData from 'form-data';
import crypto from 'crypto';
import fs from 'fs';
import { pipeline } from 'stream/promises';
import { join } from 'path';
import { randomBytes } from 'crypto';


const DEBUG_MODE = process.env.DEBUG_MODE === 'true';

/**
 * Logger utility function
 * @param {string} type - Log type (INFO, DEBUG, ERROR, etc.)
 * @param {string} message - Message to log
 * @param {string} color - ANSI color code
 */
function log(type, message, color = COLORS.RESET) {
  const timestamp = new Date().toTimeString().split(' ')[0];
  if (type === 'DEBUG' && !DEBUG_MODE) return;
  console.log(`${color}[${timestamp}] [${type}] ${message}${COLORS.RESET}`);
}

const COLORS = {
  RESET: '\x1b[0m',
  BLACK: '\x1b[30m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
  DIM: '\x1b[2m',
  BG_BLACK: '\x1b[40m',
  BG_RED: '\x1b[41m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
  BG_BLUE: '\x1b[44m',
  BG_MAGENTA: '\x1b[45m',
  BG_CYAN: '\x1b[46m',
  BG_WHITE: '\x1b[47m',
};

export class OpenDriveClient {
  constructor() {
    this.baseURL = 'https://dev.opendrive.com/api/v1';
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Authenticate with OpenDrive API
   * @throws {Error} If authentication fails
   */
  async authenticate() {
    try {
      if (!process.env.OPENDRIVE_USERNAME || !process.env.OPENDRIVE_PASSWORD) {
        throw new Error('OpenDrive credentials not found in environment variables');
      }

      log('DEBUG', 'Starting authentication...', COLORS.GRAY);
      const authData = {
        grant_type: 'password',
        client_id: 'OpenDrive',
        username: process.env.OPENDRIVE_USERNAME,
        password: process.env.OPENDRIVE_PASSWORD
      };
      log('DEBUG', `Auth request data: ${JSON.stringify({ ...authData, password: '*****' })}`, COLORS.GRAY);

      const response = await axios.post(`${this.baseURL}/oauth2/grant.json`, authData);

      if (!response.data.access_token) {
        throw new Error('No access token received from OpenDrive');
      }

      this.accessToken = response.data.access_token;
      this.refreshToken = response.data.refresh_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in * 1000);

      log('DEBUG', `Auth response: ${JSON.stringify({
        ...response.data,
        access_token: '****',
        refresh_token: '****'
      })}`, COLORS.GRAY);

      await this.testAuthentication();
    } catch (error) {
      log('ERROR', `Authentication request failed:`, COLORS.RED);
      log('ERROR', `URL: ${this.baseURL}/oauth2/grant.json`, COLORS.RED);
      log('ERROR', `Status: ${error.response?.status}`, COLORS.RED);
      log('ERROR', `Response: ${JSON.stringify(error.response?.data)}`, COLORS.RED);

      if (error.response) {
        throw new Error(`Authentication failed: ${error.response.data.error_description || error.response.data.message || error.message}`);
      }
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Test if the current authentication token is valid
   * @throws {Error} If token validation fails
   */
  async testAuthentication() {
    try {
      log('DEBUG', 'Testing authentication token...', COLORS.GRAY);
      await axios.get(`${this.baseURL}/users/info.json/OAUTH`, {
        params: { access_token: this.accessToken }
      });
      log('DEBUG', 'Token test successful', COLORS.GRAY);
    } catch (error) {
      log('ERROR', 'Token validation failed:', COLORS.RED);
      log('ERROR', error.message, COLORS.RED);
      throw error;
    }
  }

  /**
   * Calculate MD5 hash of a file using streams
   * @param {string} filePath - Path to the file
   * @returns {Promise<string>} MD5 hash of the file
   */
  async calculateFileHash(filePath) {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('md5');
      const stream = fs.createReadStream(filePath);

      stream.on('data', data => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', error => reject(error));
    });
  }

  /**
   * Get file size
   * @param {string} filePath - Path to the file
   * @returns {Promise<number>} File size in bytes
   */
  async getFileSize(filePath) {
    const stats = await fs.promises.stat(filePath);
    return stats.size;
  }

  /**
   * Create a new file on OpenDrive
   * @param {string} fileName - Name of the file
   * @param {string} folderId - ID of the folder to create file in
   * @param {number} fileSize - Size of the file in bytes
   * @param {string} fileHash - MD5 hash of the file
   * @returns {Promise<Object>} File creation response
   */
  async createFile(fileName, folderId = '0', fileSize = null, fileHash = null) {
    const createData = {
      session_id: 'OAUTH',
      folder_id: folderId,
      file_name: fileName,
      open_if_exists: 1
    };

    if (fileSize !== null && fileHash !== null) {
      createData.file_size = fileSize;
      createData.file_hash = fileHash;
    }

    log('DEBUG', `Create file request data: ${JSON.stringify(createData)}`, COLORS.GRAY);

    try {
      const response = await axios.post(
        `${this.baseURL}/upload/create_file.json`,
        createData,
        {
          params: { access_token: this.accessToken }
        }
      );

      log('DEBUG', `Create file response: ${JSON.stringify(response.data)}`, COLORS.GRAY);
      return response.data;
    } catch (error) {
      log('ERROR', `Create file request failed:`, COLORS.RED);
      log('ERROR', `URL: ${this.baseURL}/upload/create_file.json`, COLORS.RED);
      log('ERROR', `Status: ${error.response?.status}`, COLORS.RED);
      log('ERROR', `Response: ${JSON.stringify(error.response?.data)}`, COLORS.RED);
      throw error;
    }
  }

  /**
   * Close file upload
   * @param {string} fileId - ID of the file
   * @param {number} fileSize - Size of the file in bytes
   * @param {string} tempLocation - Temporary location of the file
   * @param {string} fileHash - MD5 hash of the file
   * @param {number} fileTime - File modification time
   * @returns {Promise<Object>} Close upload response
   */
  async closeFileUpload(fileId, fileSize, tempLocation, fileHash = null, fileTime = Math.floor(Date.now() / 1000)) {
    const closeData = {
      session_id: 'OAUTH',
      file_id: fileId,
      file_size: fileSize,
      temp_location: tempLocation,
      file_time: fileTime
    };

    if (fileHash) {
      closeData.file_hash = fileHash;
    }

    log('DEBUG', `Close upload request data: ${JSON.stringify(closeData)}`, COLORS.GRAY);

    try {
      const response = await axios.post(
        `${this.baseURL}/upload/close_file_upload.json`,
        closeData,
        {
          params: { access_token: this.accessToken }
        }
      );

      log('DEBUG', `Close upload response: ${JSON.stringify(response.data)}`, COLORS.GRAY);
      return response.data;
    } catch (error) {
      log('ERROR', `Close upload request failed:`, COLORS.RED);
      log('ERROR', `URL: ${this.baseURL}/upload/close_file_upload.json`, COLORS.RED);
      log('ERROR', `Status: ${error.response?.status}`, COLORS.RED);
      log('ERROR', `Response: ${JSON.stringify(error.response?.data)}`, COLORS.RED);
      throw error;
    }
  }

  /**
   * Upload a file to OpenDrive
   * @param {string|Buffer} filePathOrBuffer - File path or buffer containing the file data
   * @param {string} fileName - Name to use for the uploaded file
   * @returns {Promise<Object>} Upload response
   */
  async uploadFile(filePath, fileName) {
    const CHUNK_SIZE = 5000 * 1024 * 1024; // 5000MB chunks
    try {
      const fileSize = await this.getFileSize(filePath);
      const fileHash = await this.calculateFileHash(filePath);

      log('INFO', `Starting upload process for ${fileName}`, COLORS.CYAN);
      log('DEBUG', `File details:`, COLORS.GRAY);
      log('DEBUG', `- Size: ${fileSize} bytes`, COLORS.GRAY);
      log('DEBUG', `- MD5 Hash: ${fileHash}`, COLORS.GRAY);

      // Create file
      const createResult = await this.createFile(fileName, '0', fileSize, fileHash);
      log('SUCCESS', `File created with ID: ${createResult.FileId}`, COLORS.GREEN);

      // Upload chunks using streams
      let chunkOffset = 0;
      let chunkIndex = 1;
      const totalChunks = Math.ceil(fileSize / CHUNK_SIZE);
      let lastProgress = 0;

      while (chunkOffset < fileSize) {
        const chunkSize = Math.min(CHUNK_SIZE, fileSize - chunkOffset);
        const progress = Math.round((chunkOffset / fileSize) * 100);

        if (progress > lastProgress) {
          log('DEBUG', `Progress: ${progress}% (${(chunkOffset / 1024 / 1024).toFixed(2)}MB / ${(fileSize / 1024 / 1024).toFixed(2)}MB)`, COLORS.GRAY);
          lastProgress = progress;
        }

        const formData = new FormData();
        formData.append('session_id', 'OAUTH');
        formData.append('file_id', createResult.FileId);
        formData.append('temp_location', createResult.TempLocation);
        formData.append('chunk_offset', chunkOffset.toString());
        formData.append('chunk_size', chunkSize.toString());

        // Create a read stream for just this chunk
        const chunkStream = fs.createReadStream(filePath, {
          start: chunkOffset,
          end: chunkOffset + chunkSize - 1
        });

        formData.append('file_data', chunkStream, {
          filename: 'chunk',
          contentType: 'application/octet-stream',
          knownLength: chunkSize
        });

        log('INFO', `Uploading chunk ${chunkIndex}/${totalChunks} (${progress}%)`, COLORS.CYAN);

        const chunkResult = await axios.post(
          `${this.baseURL}/upload/upload_file_chunk.json`,
          formData,
          {
            params: { access_token: this.accessToken },
            headers: {
              ...formData.getHeaders()
            },
            maxContentLength: Infinity,
            maxBodyLength: Infinity
          }
        );

        if (chunkResult.data.TotalWritten !== chunkSize) {
          throw new Error(`Chunk size mismatch - Expected: ${chunkSize}, Got: ${chunkResult.data.TotalWritten}`);
        }

        chunkOffset += chunkSize;
        chunkIndex++;

        // Add small delay between chunks
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Close upload
      log('INFO', 'Finalizing upload...', COLORS.CYAN);
      const closeResult = await this.closeFileUpload(
        createResult.FileId,
        fileSize,
        createResult.TempLocation,
        fileHash
      );

      log('SUCCESS', 'Upload completed successfully', COLORS.GREEN);
      log('DEBUG', `Final file info:`, COLORS.GRAY);
      log('DEBUG', `- Download Link: ${closeResult.DownloadLink}`, COLORS.GRAY);
      log('DEBUG', `- Streaming Link: ${closeResult.StreamingLink}`, COLORS.GRAY);

      return closeResult;

    } catch (error) {
      log('ERROR', `Upload process failed:`, COLORS.RED);
      if (error.response) {
        log('ERROR', `Status: ${error.response.status}`, COLORS.RED);
        log('ERROR', `Response: ${JSON.stringify(error.response.data)}`, COLORS.RED);
      }
      throw new Error(`Upload failed: ${error.response?.data?.message || error.message}`);
    }
  }
}