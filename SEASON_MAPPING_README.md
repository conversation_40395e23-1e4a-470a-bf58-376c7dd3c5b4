# SubsPlease Season Mapping System

This system provides reverse transformation functionality for anime titles that have season mappings. It's designed to handle cases where anime episodes are numbered continuously across seasons, but torrent sites use season-specific numbering.

## Overview

The system consists of three main components:

1. **Season Mapping Configuration** (`_resources/subsplease_season_mapping.json`)
2. **Reverse Transformation Module** (`_resources/reverse_season_mapping.js`)
3. **Management Utility** (`manage_season_mappings.js`)

## How It Works

### Example Scenario
- **Original Anime**: "Sono Bisque Doll wa Koi wo Suru"
- **Season 2 starts at episode 13**
- **lycoris.cafe filename**: `lycoris.cafe_Sono_Bisque_Doll_wa_Koi_wo_Suru_Season_2_-_01.ass`
- **For torrent search**: Need to search for "Sono Bisque Doll wa Koi wo Suru" episode "13"

### Transformation Process
1. Parse the lycoris.cafe filename to extract title and episode number
2. Check if the title matches any season mapping
3. If found, calculate the original episode number: `(startEpisode - 1) + seasonEpisode`
4. Return the original title and calculated episode number for torrent searching

## Configuration File Structure

```json
{
  "_comment": "SubsPlease Season Mapping Configuration",
  "_description": "This file maps anime titles to their season information for SubsPlease releases. When an episode number is >= startEpisode, the title will be modified to include the season name.",
  "_example": "If Sono Bisque Doll wa Koi wo Suru has startEpisode: 13 and seasonTitle: Sono Bisque Doll wa Koi wo Suru Season 2, then episode 13+ will use the seasonTitle",
  "mappings": {
    "Sono Bisque Doll wa Koi wo Suru": {
      "startEpisode": 13,
      "seasonTitle": "Sono Bisque Doll wa Koi wo Suru Season 2"
    }
  }
}
```

## Management Commands

### Add a Season Mapping
```bash
node manage_season_mappings.js add "Original Title" <start_episode> "Season Title"
```

**Example:**
```bash
node manage_season_mappings.js add "Sono Bisque Doll wa Koi wo Suru" 13 "Sono Bisque Doll wa Koi wo Suru Season 2"
```

### List All Mappings
```bash
node manage_season_mappings.js list
```

### Remove a Mapping
```bash
node manage_season_mappings.js remove "Original Title"
```

**Example:**
```bash
node manage_season_mappings.js remove "Sono Bisque Doll wa Koi wo Suru"
```

### Test a Transformation
```bash
node manage_season_mappings.js test "filename"
```

**Example:**
```bash
node manage_season_mappings.js test "lycoris.cafe_Sono_Bisque_Doll_wa_Koi_wo_Suru_Season_2_-_01.ass"
```

## Supported Filename Formats

The system supports multiple lycoris.cafe filename formats:

1. `lycoris.cafe_Title_Name_-_01.ass`
2. `[lycoris.cafe] Title Name - 01.ass`
3. `[lycoris.cafe] Title Name - 01 [EN].ass`

## Integration

The system is automatically integrated into the torrent search functionality in `commands/upload.js`. When searching for torrents, the system will:

1. Check if the anime title needs reverse season mapping
2. Transform the title and episode number if needed
3. Use the transformed values for torrent searching
4. Log the transformation for debugging

## Files Modified

- `commands/upload.js`: Added import and integration with torrent search
- `_resources/subsplease_season_mapping.json`: Configuration file (created)
- `_resources/reverse_season_mapping.js`: Core transformation logic (created)
- `manage_season_mappings.js`: Management utility (created)

## Usage in Code

```javascript
import { transformForTorrentSearch } from './_resources/reverse_season_mapping.js';

const filename = "lycoris.cafe_Sono_Bisque_Doll_wa_Koi_wo_Suru_Season_2_-_01.ass";
const result = transformForTorrentSearch(filename);

if (result) {
  console.log(`Search for: ${result.title} episode ${result.episode}`);
  // Use result.title and result.episode for torrent searching
}
```

## Troubleshooting

### Common Issues

1. **Transformation not working**: Check if the season mapping exists using `node manage_season_mappings.js list`
2. **File parsing errors**: Ensure the filename follows one of the supported formats
3. **Configuration file errors**: Verify the JSON syntax in `_resources/subsplease_season_mapping.json`

### Debug Information

The system provides detailed logging:
- `[INFO]` messages show successful transformations
- `[WARN]` messages indicate missing configuration files
- `[ERROR]` messages show parsing or file access errors

## Adding New Anime

When a new anime needs season mapping:

1. Identify the original anime title
2. Determine the episode number where the new season starts
3. Define the season title format
4. Add the mapping using the management utility

**Example for a hypothetical anime:**
```bash
node manage_season_mappings.js add "My Hero Academia" 26 "My Hero Academia Season 2"
```

This would map episodes 26+ of "My Hero Academia" to search for the original "My Hero Academia" series with the correct episode numbers.
