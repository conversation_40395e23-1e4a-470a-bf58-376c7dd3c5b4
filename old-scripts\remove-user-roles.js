// src/events/ready/remove-user-roles.js

// Target user ID to monitor
const TARGET_USER_ID = '703347680042156103';
// Target guild ID to monitor
const TARGET_GUILD_ID = '739837176795103235';

async function removeAllRolesFromUser(guild, userId) {
  try {
    // Check if this is the target guild
    if (guild.id !== TARGET_GUILD_ID) {
      return;
    }

    // Fetch the guild member
    const member = await guild.members.fetch(userId);

    if (!member) {
      console.log(`User ${userId} not found in guild ${guild.name}`);
      return;
    }

    // Get removable roles (roles that the bot can manage)
    const removableRoles = member.roles.cache.filter(role =>
      role.id !== guild.id && // Don't remove @everyone role
      role.editable    // Only include roles that the bot can remove
    );

    if (removableRoles.size === 0) {
      console.log(`No removable roles found for user ${userId} in guild ${guild.name}`);
      return;
    }

    // Remove all roles
    await member.roles.remove(removableRoles);
    console.log(`Removed ${removableRoles.size} roles from user ${userId} in guild ${guild.name}`);
  } catch (error) {
    console.error(`Error removing roles from user ${userId} in guild ${guild.name || 'unknown'}:`, error);
  }
}

export default (c, client, handler) => {
  console.log(`${c.user.username} is ready!`);

  // Find the specific guild
  const targetGuild = client.guilds.cache.get(TARGET_GUILD_ID);

  if (targetGuild) {
    console.log(`Found target guild: ${targetGuild.name}`);
    // Remove all roles from the target user in the specific guild
    removeAllRolesFromUser(targetGuild, TARGET_USER_ID);
  } else {
    console.log(`Target guild ${TARGET_GUILD_ID} not found or bot is not a member`);
  }
};