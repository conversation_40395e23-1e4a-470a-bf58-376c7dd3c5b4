/**
 * @file Update Forum Posts Command
 * @description This script fetches Discord forum posts and maps them to AniList IDs
 * Key features include:
 * - Fetching all threads from a specified forum channel
 * - Searching AniList GraphQL API to find matching anime
 * - Creating a mapping between AniList IDs and forum post IDs
 * - Saving the mapping to a JSON file with rate limiting
 *
 * @requires dotenv
 * @requires @discordjs/builders
 * @requires fs/promises
 * @requires path
 */

import { config } from 'dotenv';
config();
import fs from 'fs/promises';
import path from 'path';

import { SlashCommandBuilder } from '@discordjs/builders';

// Forum channel ID to fetch threads from
const FORUM_CHANNEL_ID = '1368731575323463880';
// Rate limit for AniList API (2 seconds)
const RATE_LIMIT_MS = 2000;

/**
 * @type {SlashCommandBuilder}
 * @description Defines the structure of the /updateforumposts slash command
 */
export const data = new SlashCommandBuilder()
  .setName('updateforumposts')
  .setDescription('Fetches forum posts and maps them to AniList IDs');

/**
 * @async
 * @function run
 * @description Handles the execution of the /updateforumposts command
 * @param {Object} params - The parameters object
 * @param {Object} params.interaction - The Discord interaction object
 * @param {Object} params.client - The Discord client object
 * @param {Object} params.handler - The command handler object
 * @returns {Promise<void>}
 */
export async function run({ interaction, client, handler }) {
  // User authentication
  if (interaction.user.id !== '351006685587963916' && interaction.user.id !== '185126303677153281') {
    await interaction.reply({
      content: 'Nie możesz użyć tej komendy.',
      ephemeral: true,
    });
    return;
  }

  await interaction.deferReply();
  await interaction.editReply('🦌 Fetching forum posts...');

  try {
    // Get the forum channel
    const forumChannel = await client.channels.fetch(FORUM_CHANNEL_ID);
    if (!forumChannel || forumChannel.type !== 15) { // 15 is the GUILD_FORUM channel type
      await interaction.editReply('Could not find the specified forum channel or it is not a forum channel.');
      return;
    }

    // Collection to store all thread objects
    let allThreads = new Map();

    // Fetch active threads
    const activeThreads = await forumChannel.threads.fetchActive();
    if (activeThreads && activeThreads.threads.size > 0) {
      activeThreads.threads.forEach((thread, threadId) => {
        allThreads.set(threadId, thread);
      });
    }

    // Fetch archived threads
    const archivedThreads = await forumChannel.threads.fetchArchived();
    if (archivedThreads && archivedThreads.threads.size > 0) {
      archivedThreads.threads.forEach((thread, threadId) => {
        if (!allThreads.has(threadId)) {
          allThreads.set(threadId, thread);
        }
      });
    }

    if (allThreads.size === 0) {
      await interaction.editReply('No threads found in the forum channel.');
      return;
    }

    await interaction.editReply(`🦌 Found ${allThreads.size} forum posts. Mapping to AniList IDs...`);
    
    // Create the mapping object in the requested format
    const forumPostMap = {
      "ANILIST_ID": "DISCORD_FORUM_POST_ID"
    };
    
    let processedCount = 0;
    let matchedCount = 0;
    
    // Directory to save the JSON file
    const resourcesDir = path.join(process.cwd(), '_resources');
    
    // Ensure the directory exists
    try {
      await fs.mkdir(resourcesDir, { recursive: true });
    } catch (dirError) {
      console.error('Error creating directory:', dirError);
    }
    
    // Process each thread
    for (const [threadId, thread] of allThreads) {
      try {
        // Clean the thread name for better matching
        const threadName = thread.name
          .replace(/[^\w\s]/gi, ' ') // Replace special characters with spaces
          .replace(/\s+/g, ' ')      // Replace multiple spaces with a single space
          .trim();                   // Remove leading/trailing whitespace
        
        // Search AniList for the anime
        const anilistData = await searchAniList(threadName);
        
        if (anilistData && anilistData.id) {
          // Add the mapping with anilist_id as string key and thread ID as string value
          forumPostMap[anilistData.id.toString()] = threadId.toString();
          matchedCount++;
        }
        
        processedCount++;
        
        // Update progress periodically
        if (processedCount % 5 === 0 || processedCount === allThreads.size) {
          await interaction.editReply(
            `🦌 Processing forum posts... ` +
            `\nProgress: ${processedCount}/${allThreads.size} ` +
            `\nMatched with AniList: ${matchedCount}`
          );
        }
        
        // Respect rate limit for AniList API
        await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_MS));
      } catch (threadError) {
        console.error(`Error processing thread ${threadId}:`, threadError);
      }
    }
    
    // Save the mapping to a JSON file
    const filePath = path.join(resourcesDir, 'forumposts.json');
    await fs.writeFile(filePath, JSON.stringify(forumPostMap, null, 2));
    
    await interaction.editReply(
      `🦌 Completed mapping forum posts to AniList IDs!\n` +
      `Total posts: ${allThreads.size}\n` +
      `Matched with AniList: ${matchedCount}\n` +
      `Mapping has been saved to '_resources/forumposts.json'`
    );
  } catch (error) {
    console.error('Error processing forum posts:', error);
    await interaction.editReply(`An error occurred: ${error.message}`);
  }
}

/**
 * @async
 * @function searchAniList
 * @description Searches AniList API for anime matching the given title
 * @param {string} title - The anime title to search for
 * @returns {Promise<Object|null>} Object with AniList ID if found, null otherwise
 */
async function searchAniList(title) {
  try {
    const query = `
      query ($search: String) {
        Media(search: $search, type: ANIME) {
          id
          title {
            romaji
            english
            native
          }
        }
      }
    `;
    
    const variables = {
      search: title
    };
    
    const response = await fetch('https://graphql.anilist.co', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables
      })
    });
    
    const data = await response.json();
    
    if (data.errors) {
      console.error('AniList API error:', data.errors);
      return null;
    }
    
    if (data.data && data.data.Media) {
      return {
        id: data.data.Media.id
      };
    }
    
    return null;
  } catch (error) {
    console.error(`Error searching AniList for "${title}":`, error);
    return null;
  }
}

/**
 * @type {Object}
 * @property {boolean} devOnly - Indicates if the command is for developers only
 * @property {boolean} deleted - Indicates if the command has been deleted
 */
export const options = {
  devOnly: true,
  deleted: false,
};