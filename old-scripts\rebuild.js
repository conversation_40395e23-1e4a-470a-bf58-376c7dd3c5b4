import { config } from 'dotenv';
config();

import fs from 'fs';
import { WebhookClient, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_PROJECT_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
const webhookUrl = process.env.PUBLIC_DISCORD_WEBHOOK;

// ============ CONFIGURATION OPTIONS ============
// Set to true to only fetch anime added after the cutoff date
const FETCH_ONLY_NEWER_ANIME = true;
// Cutoff date for fetching newer anime
const DATE_CUTOFF = '2025-04-29 20:51:31.808443+00';
// =============================================

// Load role pings from JSON file
let rolePings;
try {
  const rolePingsPath = './_resources/role_pings.json';
  const rolePingsData = fs.readFileSync(rolePingsPath, 'utf8');
  rolePings = JSON.parse(rolePingsData);
  console.log('Role pings loaded successfully');
} catch (error) {
  console.error(`Error loading role_pings.json: ${error}`);
  rolePings = {};
}

// Fetch anime data based on configuration settings
async function fetchAnimeData() {
  let animeData = [];
  
  if (FETCH_ONLY_NEWER_ANIME) {
    // Only fetch anime records created on or after the cutoff date
    console.log(`Fetching only anime episodes added on or after ${DATE_CUTOFF}`);
    let { data: animeQueryNewer, error: errorNewer } = await supabase
      .from('anime')
      .select('id, anime_title, episode_number, primary_source, thumbnail_link, anilist_id')
      .gte('date_added', DATE_CUTOFF)
      .order('date_added', { ascending: true });
    
    if (errorNewer) {
      console.error(`Error querying newer anime data: ${errorNewer}`);
      process.exit(1);
    }
    
    animeData = animeQueryNewer;
    console.log(`Fetched ${animeData.length} records`);
  } else {
    // Fetch both older and newer anime records
    console.log(`Fetching all anime episodes (split by ${DATE_CUTOFF})`);
    
    // First fetch older data (up to 1000 rows)
    let { data: animeQueryOlder, error: errorOlder } = await supabase
      .from('anime')
      .select('id, anime_title, episode_number, primary_source, thumbnail_link, anilist_id')
      .lt('date_added', DATE_CUTOFF)
      .order('date_added', { ascending: true });
    
    if (errorOlder) {
      console.error(`Error querying older anime data: ${errorOlder}`);
      process.exit(1);
    }
    
    // Then fetch newer data (after the cutoff date)
    let { data: animeQueryNewer, error: errorNewer } = await supabase
      .from('anime')
      .select('id, anime_title, episode_number, primary_source, thumbnail_link, anilist_id')
      .gte('date_added', DATE_CUTOFF)
      .order('date_added', { ascending: true });
    
    if (errorNewer) {
      console.error(`Error querying newer anime data: ${errorNewer}`);
      process.exit(1);
    }
    
    // Combine both sets of results
    animeData = [...animeQueryOlder, ...animeQueryNewer];
    console.log(`Fetched ${animeQueryOlder.length} older records and ${animeQueryNewer.length} newer records`);
    console.log(`Total records: ${animeData.length}`);
  }
  
  return animeData;
}

// Fetch anime data based on configuration
const animeQuery = await fetchAnimeData();

// Process all anime episodes
for (let i = 0; i < animeQuery.length; i++) {
  let query = animeQuery[i];

  // Get the hidden property from anime_metadata table
  let { data: metadataQuery, error: metadataError } = await supabase
    .from('anime_metadata')
    .select('hidden')
    .eq('anilist_id', query.anilist_id)
    .single();

  if (metadataError && metadataError.code !== 'PGRST116') {
    console.error(`Error fetching metadata for ID ${query.anilist_id}:`, metadataError);
  }

  const isHidden = metadataQuery?.hidden || false;

  // Skip if the anime is hidden
  if (isHidden) {
    console.log(`Skipping hidden anime: ${query.anime_title} (${query.anilist_id}): Episode ${query.episode_number}`);
    continue;
  }

  const episodeEmoji = numberToDiscordEmoji(query.episode_number.toString().padStart(2, '0'));
  const rolePing = idToRolePing(query.anilist_id);

  console.log(`Processing ${query.anime_title} (${query.anilist_id}): Episode ${query.episode_number}`);

  await publishToDiscord(
    query.anilist_id,
    query.anime_title,
    episodeEmoji,
    rolePing,
    query.thumbnail_link,
    query.episode_number
  );
  await sleep(2000);
}

async function publishToDiscord(
  animeId,
  animeTitle,
  episodeEmoji,
  rolePing,
  thumbnailUrl,
  episodeNumber
) {
  const webhookClient = new WebhookClient({ url: webhookUrl });

  let animeTitleLink = animeTitle.toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

  const embed = new EmbedBuilder()
    .setTitle(`${episodeEmoji} ${animeTitle.replaceAll('_', ' ')}`)
    .setDescription(
      `**Obejrzyj online: [[KLIKNIJ TUTAJ!]](https://www.lycoris.cafe/anime/${animeId}/${animeTitleLink}/watch/${Number(episodeNumber)}) \nPobierz odcinek: [PRZYCISKI PONIŻEJ]**`
    )
    .setImage(thumbnailUrl);

  // Create buttons using animeId instead of title
  const button1 = new ButtonBuilder()
    .setLabel('MKV')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=mkv`)
    .setStyle(ButtonStyle.Link);
  const button2 = new ButtonBuilder()
    .setLabel('1080p')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=1080p`)
    .setStyle(ButtonStyle.Link);
  const button3 = new ButtonBuilder()
    .setLabel('720p')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=720p`)
    .setStyle(ButtonStyle.Link);
  const button4 = new ButtonBuilder()
    .setLabel('480p')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=480p`)
    .setStyle(ButtonStyle.Link);
  const button5 = new ButtonBuilder()
    .setLabel('Napisy PL')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=pl`)
    .setStyle(ButtonStyle.Link);
  const row = new ActionRowBuilder().addComponents(button1, button2, button3, button4, button5);

  const messageOptions = {
    embeds: [embed],
    components: [row],
    allowed_mentions: {
      parse: ['everyone', 'users'],
    }
  };

  // Only add the content field if rolePing exists
  if (rolePing) {
    messageOptions.content = rolePing;
  }

  try {
    await webhookClient.send(messageOptions);
    console.log(`Message sent for ${animeTitle} - Episode ${episodeNumber}`);
  } catch (error) {
    console.error(`Error sending webhook: ${error}`);
  }
}

function numberToDiscordEmoji(num) {
  const emojiMap = {
    0: ':zero: ',
    1: ':one: ',
    2: ':two: ',
    3: ':three: ',
    4: ':four: ',
    5: ':five: ',
    6: ':six: ',
    7: ':seven: ',
    8: ':eight: ',
    9: ':nine: ',
  };

  return String(num)
    .split('')
    .map((digit) => emojiMap[digit])
    .join('');
}

function idToRolePing(anilistId) {
  if (!anilistId || !rolePings) return null;

  // Convert anilistId to string for JSON key lookup
  const id = String(anilistId);
  return rolePings[id] || null;
}

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}