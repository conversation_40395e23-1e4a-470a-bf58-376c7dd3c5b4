/**
 * @file Discord Bo<PERSON> Script for File Download and Decryption
 * @description This script implements a Discord bot command for downloading and decrypting files from pixeldrain.com.
 * It includes the following features:
 * - Environment variable configuration using dotenv
 * - Slash command setup for Discord interaction
 * - User authentication for command usage
 * - File download from pixeldrain.com
 * - Custom decryption algorithm
 * - Error handling and user feedback
 *
 * The script uses a custom Base64 encoding and a simple Caesar cipher for basic encryption.
 *
 * @requires dotenv
 * @requires @discordjs/builders
 * @requires discord.js
 */

import dotenv from 'dotenv';
dotenv.config();

import { SlashCommandBuilder } from '@discordjs/builders';
import { AttachmentBuilder } from 'discord.js';

// Custom Base64 character set for encoding/decoding
const BASE64_CHARS = '6HOXGZkuYQKba084JgjVy+9driFE7INcDUhL/Mmqx2WPBCS5p1slvReAwotf3znT';
// Standard Base64 character set for reference
const STANDARD_BASE64_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

/**
 * @type {SlashCommandBuilder}
 * @description Defines the structure of the /download slash command
 */
export const data = new SlashCommandBuilder()
  .setName('download')
  .setDescription('🦌')
  .addStringOption((option) => option.setName('file').setDescription('🦌').setRequired(true));

/**
 * @async
 * @function run
 * @description Handles the execution of the /download command
 * @param {Object} params - The parameters object
 * @param {Object} params.interaction - The Discord interaction object
 * @param {Object} params.client - The Discord client object
 * @param {Object} params.handler - The command handler object
 * @returns {Promise<void>}
 */
export async function run({ interaction, client, handler }) {
  // Check if the user is authorized to use this command
  if (interaction.user.id !== '351006685587963916' && interaction.user.id !== '185126303677153281') {
    await interaction.reply({
      content: 'Nie możesz użyć tej komendy.',
      ephemeral: true,
    });
    return;
  }

  await interaction.deferReply();

  const fileLink = interaction.options.getString('file').replace('?download', '');

  // Validate the provided link
  if (!fileLink.includes('pixeldrain.com/api/file')) {
    await interaction.editReply(`Dałeś zły link baka. Daj link z pixeldrian.com a nie \`${fileLink}\``);
    return;
  }

  try {
    if (fileLink) {
      // Download and process the file
      const response = await fetch(fileLink);
      const buffer = await response.arrayBuffer();

      const textDecoder = new TextDecoder();
      const subtitleData = textDecoder.decode(buffer);
      const decryptedData = decodeCustom(subtitleData);

      // Create a buffer from the decrypted data
      const decryptedBuffer = Buffer.from(decryptedData, 'utf-8');

      // Fetch file metadata
      const fetchFile = await fetch(`${fileLink}/info`);
      const fileData = await fetchFile.json();

      // Create an attachment with the decrypted data
      const attachment = new AttachmentBuilder(decryptedBuffer, { name: fileData.name });

      // Send the attachment as a reply
      await interaction.editReply({
        files: [attachment],
      });
    } else {
      await interaction.editReply('No file link was specified.');
      return;
    }
  } catch (error) {
    console.error(`Error processing file: ${error}`);
    await interaction.editReply(
      `An error occurred while processing the file.\n\`\`\`${error}\`\`\`\n<@351006685587963916> napraw`
    );
  }
}

/**
 * @type {Object}
 * @property {boolean} devOnly - Indicates if the command is for developers only
 * @property {boolean} deleted - Indicates if the command has been deleted
 */
export const options = {
  devOnly: true,
  deleted: false,
};

/**
 * @function decodeCustom
 * @description Decodes the custom-encrypted data
 * @param {string} encoded - The encoded string to decrypt
 * @returns {string} The decrypted string or an error message
 */
function decodeCustom(encoded) {
  // Step 1: Reverse Caesar cipher
  let decoded = encoded
    .split('')
    .map((char) => {
      const index = BASE64_CHARS.indexOf(char);
      return index === -1 ? char : BASE64_CHARS[(index - 3 + BASE64_CHARS.length) % BASE64_CHARS.length];
    })
    .join('');

  // Step 2: Reverse the string
  decoded = decoded.split('').reverse().join('');

  // Step 3: Convert custom Base64 to standard Base64
  decoded = decoded
    .split('')
    .map((char) => {
      const index = BASE64_CHARS.indexOf(char);
      return index === -1 ? char : STANDARD_BASE64_CHARS[index];
    })
    .join('');

  try {
    // Step 4: Decode Base64 to binary data
    const binaryData = atob(decoded);

    // Step 5: Convert binary data to Uint8Array
    const bytes = new Uint8Array(binaryData.length);
    for (let i = 0; i < binaryData.length; i++) {
      bytes[i] = binaryData.charCodeAt(i);
    }

    // Step 6: Decode Uint8Array to UTF-8 string
    const decodedText = new TextDecoder('utf-8').decode(bytes);
    return decodedText;
  } catch (e) {
    console.error('Decoding failed:', e);
    return 'Decoding failed. Invalid input.';
  }
}
