import dotenv from 'dotenv';
dotenv.config();

import { SlashCommandBuilder } from '@discordjs/builders';
import { AttachmentBuilder, EmbedBuilder } from 'discord.js';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_PROJECT_URL, process.env.SUPABASE_ADMIN_KEY);

const BASE64_CHARS = '6HOXGZkuYQKba084JgjVy+9driFE7INcDUhL/Mmqx2WPBCS5p1slvReAwotf3znT';
const STANDARD_BASE64_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

export const data = new SlashCommandBuilder()
  .setName('pobierz')
  .setDescription('Pobierz odcinek wraz z napisami')
  .addStringOption((option) => option.setName('id').setDescription('ID Odcinka').setRequired(true));

export async function run({ interaction, client, handler }) {
  await interaction.deferReply({ ephemeral: true });

  const fileId = interaction.options.getString('id');

  try {
    let maintenenceMode = false;
    if (maintenenceMode) {
      await interaction.editReply('Możliwość pobierania plików została tymczasowo wyłączona.');
      return;
    }

    // const userID = interaction.user.id;
    // const member = await interaction.guild.members.fetch(userID);

    // if (!member.roles.cache.has('1262525691086504118') || !member.roles.cache.has('1261783782042767401')) {
    //   await interaction.editReply(
    //     'Napisz do <@351006685587963916> by się zweryfikować i móc pobierać pliki z tego serwera. Pinguj mnie na innym kanale jak nie odpowiem w ciągu godziny.'
    //   );
    //   return;
    // }

    const accountCreatedDays = daysSince(interaction.user.createdAt);
    const serverJoinedDays = daysSince(interaction.member.joinedAt);

    // // Check if user is banned
    // const { data: banData, error: banError } = await supabase
    //   .from('dl_bans')
    //   .select('user_id')
    //   .eq('user_id', interaction.user.id);

    // if (banError) throw new Error(JSON.stringify(banError));
    // if (banData.length > 0) {
    //   await interaction.editReply('Możliwość pobierania plików została tymczasowo wyłączona.');
    //   return;
    // }

    // Fetch video and subtitle links
    const { data: animeData, error: animeError } = await supabase.from('anime').select('primary_source->FHD').eq('id', fileId);

    if (animeError) throw new Error(JSON.stringify(animeError));

    const { data: subtitleData, error: subtitleError } = await supabase
      .from('subtitles')
      .select('subtitleLinks, is_encrypted')
      .eq('id', Number(fileId) - 1);

    if (subtitleError) throw new Error(JSON.stringify(subtitleError));

    if (animeData.length === 0 || subtitleData.length === 0) {
      await interaction.editReply('Odcinka nie znaleziono.');
      return;
    }

    const videoLink = animeData[0].FHD;
    const subtitleLink = subtitleData[0].subtitleLinks.PL;

    // Process subtitles
    const fetchSubMetadata = await fetch(`${subtitleLink}/info`);
    const subtitlesMetadata = await fetchSubMetadata.json();

    const subtitleResponse = await fetch(subtitleLink);
    const subtitleBuffer = await subtitleResponse.arrayBuffer();
    const subtitleText = new TextDecoder().decode(subtitleBuffer);
    let decryptedSubtitles;
    if (subtitleData[0].is_encrypted) {
      decryptedSubtitles = decodeCustom(subtitleText);
    } else {
      decryptedSubtitles = subtitleText;
    }

    // Create subtitle attachment
    const subtitleAttachment = new AttachmentBuilder(Buffer.from(decryptedSubtitles, 'utf-8'), {
      name: subtitlesMetadata.name,
    });

    let replyContent = `\nWideo: ${videoLink}?download\nNapisy:`;

    await interaction.editReply({
      content: replyContent,
      files: [subtitleAttachment],
      ephemeral: true,
    });

    const logEmbed = new EmbedBuilder()
      .setColor('#0099ff')
      .setTitle('Download Log')
      .setThumbnail(interaction.user.displayAvatarURL())
      .addFields(
        { name: 'User', value: `<@${interaction.user.id}> - ${interaction.user.tag} (${interaction.user.id})` },
        { name: 'Account Created', value: `${interaction.user.createdAt.toUTCString()} (${accountCreatedDays} days ago)` },
        { name: 'Joined Server', value: `${interaction.member.joinedAt.toUTCString()} (${serverJoinedDays} days ago)` },
        { name: 'Episode ID', value: fileId },
        { name: 'File Name', value: subtitlesMetadata.name }
      )
      .setTimestamp();
    const logChannel = await client.channels.fetch('1260705584357969982');
    await logChannel.send({ embeds: [logEmbed] });

    console.log(`User ${interaction.user.tag} (${interaction.user.id}) downloaded episode ${fileId}`);
  } catch (error) {
    console.error(`Error processing download: ${error}`);
    await interaction.editReply(`Wystąpił problem podczas pobierania odcinka :(. Poproś <@351006685587963916> o pomoc.`);
  }
}

function daysSince(date) {
  const now = new Date();
  const diffTime = Math.abs(now - date);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

function decodeCustom(encoded) {
  // Step 1: Reverse Caesar cipher
  let decoded = encoded
    .split('')
    .map((char) => {
      const index = BASE64_CHARS.indexOf(char);
      return index === -1 ? char : BASE64_CHARS[(index - 3 + BASE64_CHARS.length) % BASE64_CHARS.length];
    })
    .join('');

  // Step 2: Reverse the string
  decoded = decoded.split('').reverse().join('');

  // Step 3: Convert custom Base64 to standard Base64
  decoded = decoded
    .split('')
    .map((char) => {
      const index = BASE64_CHARS.indexOf(char);
      return index === -1 ? char : STANDARD_BASE64_CHARS[index];
    })
    .join('');

  try {
    // Step 4: Decode Base64 to binary data
    const binaryData = atob(decoded);

    // Step 5: Convert binary data to Uint8Array
    const bytes = new Uint8Array(binaryData.length);
    for (let i = 0; i < binaryData.length; i++) {
      bytes[i] = binaryData.charCodeAt(i);
    }

    // Step 6: Decode Uint8Array to UTF-8 string
    const decodedText = new TextDecoder('utf-8').decode(bytes);
    return decodedText;
  } catch (e) {
    console.error('Decoding failed:', e);
    return 'Decoding failed. Invalid input.';
  }
}

export const options = {
  devOnly: true,
  deleted: false,
};
