/**
 * @file Season Mapping Management Utility
 * @description This script provides a command-line interface to manage SubsPlease season mappings.
 * It allows adding, removing, and listing season mappings for anime titles.
 * 
 * Usage:
 * node manage_season_mappings.js add "Sono Bisque Doll wa Koi wo Suru" 13 "Sono Bisque Doll wa Koi wo Suru Season 2"
 * node manage_season_mappings.js list
 * node manage_season_mappings.js remove "Sono Bisque Doll wa Koi wo Suru"
 * node manage_season_mappings.js test "lycoris.cafe_Sono_Bisque_Doll_wa_Koi_wo_Suru_Season_2_-_01.ass"
 */

import { addSeasonMapping, transformForTorrentSearch } from './_resources/reverse_season_mapping.js';
import fs from 'fs';
import path from 'path';

const MAPPING_FILE = './_resources/subsplease_season_mapping.json';

/**
 * Loads the current season mappings from file
 * @returns {Object} The season mappings configuration
 */
function loadMappings() {
  try {
    const mappingPath = path.resolve(MAPPING_FILE);
    const mappingData = fs.readFileSync(mappingPath, 'utf8');
    return JSON.parse(mappingData);
  } catch (error) {
    console.warn('[WARN] Could not load season mappings, creating new file');
    return {
      "_comment": "SubsPlease Season Mapping Configuration",
      "_description": "This file maps anime titles to their season information for SubsPlease releases. When an episode number is >= startEpisode, the title will be modified to include the season name.",
      "_example": "If Sono Bisque Doll wa Koi wo Suru has startEpisode: 13 and seasonTitle: Sono Bisque Doll wa Koi wo Suru Season 2, then episode 13+ will use the seasonTitle",
      "mappings": {}
    };
  }
}

/**
 * Saves the season mappings to file
 * @param {Object} config - The season mappings configuration
 */
function saveMappings(config) {
  try {
    const mappingPath = path.resolve(MAPPING_FILE);
    fs.writeFileSync(mappingPath, JSON.stringify(config, null, 2));
    console.log('[INFO] Season mappings saved successfully');
  } catch (error) {
    console.error(`[ERROR] Could not save season mappings: ${error.message}`);
  }
}

/**
 * Lists all current season mappings
 */
function listMappings() {
  const config = loadMappings();
  const mappings = config.mappings || {};
  
  if (Object.keys(mappings).length === 0) {
    console.log('No season mappings found.');
    return;
  }
  
  console.log('Current season mappings:');
  console.log('='.repeat(50));
  
  for (const [originalTitle, mapping] of Object.entries(mappings)) {
    console.log(`Original Title: ${originalTitle}`);
    console.log(`  Season Title: ${mapping.seasonTitle}`);
    console.log(`  Start Episode: ${mapping.startEpisode}`);
    console.log('-'.repeat(30));
  }
}

/**
 * Adds a new season mapping
 * @param {string} originalTitle - The original anime title
 * @param {number} startEpisode - The episode number where the new season starts
 * @param {string} seasonTitle - The title used for the new season
 */
function addMapping(originalTitle, startEpisode, seasonTitle) {
  const config = loadMappings();
  
  if (!config.mappings) {
    config.mappings = {};
  }
  
  config.mappings[originalTitle] = {
    startEpisode: parseInt(startEpisode, 10),
    seasonTitle: seasonTitle
  };
  
  saveMappings(config);
  console.log(`[INFO] Added season mapping: "${originalTitle}" -> "${seasonTitle}" (starts at episode ${startEpisode})`);
}

/**
 * Removes a season mapping
 * @param {string} originalTitle - The original anime title to remove
 */
function removeMapping(originalTitle) {
  const config = loadMappings();
  
  if (!config.mappings || !config.mappings[originalTitle]) {
    console.log(`[WARN] No mapping found for "${originalTitle}"`);
    return;
  }
  
  delete config.mappings[originalTitle];
  saveMappings(config);
  console.log(`[INFO] Removed season mapping for "${originalTitle}"`);
}

/**
 * Tests the reverse transformation for a given filename
 * @param {string} filename - The lycoris.cafe filename to test
 */
function testTransformation(filename) {
  console.log(`Testing transformation for: ${filename}`);
  console.log('-'.repeat(50));
  
  const result = transformForTorrentSearch(filename);
  
  if (result) {
    console.log(`✓ Transformation successful:`);
    console.log(`  Search Title: "${result.title}"`);
    console.log(`  Search Episode: ${result.episode}`);
  } else {
    console.log(`✗ Transformation failed`);
  }
}

/**
 * Displays usage information
 */
function showUsage() {
  console.log('Season Mapping Management Utility');
  console.log('='.repeat(40));
  console.log('Usage:');
  console.log('  node manage_season_mappings.js add <original_title> <start_episode> <season_title>');
  console.log('  node manage_season_mappings.js list');
  console.log('  node manage_season_mappings.js remove <original_title>');
  console.log('  node manage_season_mappings.js test <filename>');
  console.log('');
  console.log('Examples:');
  console.log('  node manage_season_mappings.js add "Sono Bisque Doll wa Koi wo Suru" 13 "Sono Bisque Doll wa Koi wo Suru Season 2"');
  console.log('  node manage_season_mappings.js list');
  console.log('  node manage_season_mappings.js remove "Sono Bisque Doll wa Koi wo Suru"');
  console.log('  node manage_season_mappings.js test "lycoris.cafe_Sono_Bisque_Doll_wa_Koi_wo_Suru_Season_2_-_01.ass"');
}

// Main execution
const args = process.argv.slice(2);

if (args.length === 0) {
  showUsage();
  process.exit(0);
}

const command = args[0].toLowerCase();

switch (command) {
  case 'add':
    if (args.length !== 4) {
      console.error('[ERROR] Add command requires 3 arguments: original_title, start_episode, season_title');
      showUsage();
      process.exit(1);
    }
    addMapping(args[1], args[2], args[3]);
    break;
    
  case 'list':
    listMappings();
    break;
    
  case 'remove':
    if (args.length !== 2) {
      console.error('[ERROR] Remove command requires 1 argument: original_title');
      showUsage();
      process.exit(1);
    }
    removeMapping(args[1]);
    break;
    
  case 'test':
    if (args.length !== 2) {
      console.error('[ERROR] Test command requires 1 argument: filename');
      showUsage();
      process.exit(1);
    }
    testTransformation(args[1]);
    break;
    
  default:
    console.error(`[ERROR] Unknown command: ${command}`);
    showUsage();
    process.exit(1);
}
