// @ts-check
import { config } from 'dotenv';
config();
import { createClient } from '@supabase/supabase-js';
import { OpenDriveClient } from '../openDrive.js';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import cron from 'node-cron';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const TEMP_DOWNLOAD_DIR = path.join(__dirname, 'temp_downloads');
const MAX_RECORDS_TO_CHECK = 10;

if (!fs.existsSync(TEMP_DOWNLOAD_DIR)) {
  fs.mkdirSync(TEMP_DOWNLOAD_DIR, { recursive: true });
}

const supabase = createClient(
  // @ts-ignore
  process.env.SUPABASE_PROJECT_URL,
  process.env.SUPABASE_ADMIN_KEY
);

const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  CYAN: '\x1b[36m',
  GRAY: '\x1b[90m',
};

async function downloadFromOpenDrive(url, outputPath) {
  try {
    console.info(`${COLORS.CYAN}[INFO] Downloading from OpenDrive: ${url}${COLORS.RESET}`);
    const response = await axios({
      method: 'GET',
      url,
      responseType: 'stream'
    });

    const writer = fs.createWriteStream(outputPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Failed to download from OpenDrive: ${error.message}${COLORS.RESET}`);
    throw error;
  }
}

async function uploadToPixeldrain(filePath, apiKey) {
  let attempts = 0;
  const maxAttempts = 3;

  while (attempts < maxAttempts) {
    attempts++;
    try {
      const fileName = path.basename(filePath);
      const apiUrl = `https://pixeldrain.com/api/file/${fileName}`;
      const fileStream = fs.createReadStream(filePath);
      console.info(`${COLORS.GRAY}[INFO] Attempt ${attempts} to upload file ${filePath}...${COLORS.RESET}`);

      const response = await fetch(apiUrl, {
        method: 'PUT',
        headers: {
          Authorization: `Basic ${Buffer.from(`:${apiKey}`).toString('base64')}`,
        },
        // @ts-ignore
        body: fileStream,
        duplex: 'half', // Add this line to fix the error
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      } else {
        const data = await response.json();
        console.info(`${COLORS.CYAN}[INFO] Upload successful for: ${filePath}${COLORS.RESET}`);
        return `https://pixeldrain.com/api/file/${data.id}`;
      }
    } catch (error) {
      console.error(`${COLORS.RED}[ERROR] Upload attempt ${attempts} encountered an error: ${error.message}${COLORS.RESET}`);
      if (attempts >= maxAttempts) {
        console.error(`${COLORS.RED}[ERROR] Giving up after ${attempts} attempts${COLORS.RESET}`);
        throw new Error(`Failed after ${attempts} attempts: ${error.message}`);
      }
      await new Promise(resolve => setTimeout(resolve, 60000));
    }
  }
}

async function processQuality(quality, url, episode) {
  try {
    let resolutionLabel;
    switch (quality) {
      case 'FHD':
        resolutionLabel = '1080p';
        break;
      case 'HD':
        resolutionLabel = '720p';
        break;
      case 'SD':
        resolutionLabel = '480p';
        break;
      case 'SourceMKV':
        resolutionLabel = '1080p';
        break;
      default:
        resolutionLabel = quality;
    }

    const fileExtension = quality === 'SourceMKV' ? 'mkv' : 'mp4';
    const filename = `[lycoris.cafe] ${episode.anime_title} - ${episode.episode_number} [${resolutionLabel}].${fileExtension}`;
    const downloadPath = path.join(TEMP_DOWNLOAD_DIR, `${episode.id}_${filename}`);

    await downloadFromOpenDrive(url, downloadPath);

    console.info(`${COLORS.CYAN}[INFO] Uploading ${filename} to Pixeldrain${COLORS.RESET}`);
    const pixeldrainUrl = await uploadToPixeldrain(downloadPath, process.env.PIXELDRAIN_API_KEY);

    fs.unlinkSync(downloadPath);

    return { quality, link: pixeldrainUrl };
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Failed to process ${quality}: ${error.message}${COLORS.RESET}`);
    throw error;
  }
}

async function processEpisode(episode) {
  try {
    const primary_source = episode.primary_source;
    const requiredQualities = ['FHD', 'HD', 'SD', 'SourceMKV'];
    const missingQualities = requiredQualities.filter(quality =>
      !primary_source[quality] || !primary_source[quality].includes('od.lk')
    );

    if (missingQualities.length > 0) {
      console.info(`${COLORS.GRAY}[INFO] Skipping episode ${episode.id} - Missing qualities: ${missingQualities.join(', ')}${COLORS.RESET}`);
      return;
    }

    const uploadPromises = Object.entries(primary_source)
      .filter(([quality, url]) => url && url.includes('od.lk'))
      .map(([quality, url]) => processQuality(quality, url, episode));

    const results = await Promise.all(uploadPromises);

    const secondary_source = results.reduce((acc, { quality, link }) => {
      acc[quality] = link;
      return acc;
    }, {});

    const { data, error } = await supabase
      .from('anime')
      .update({ secondary_source })
      .eq('id', episode.id);

    if (error) {
      throw new Error(`Database update failed: ${error.message}`);
    }

    console.info(`${COLORS.GREEN}[SUCCESS] Successfully processed episode ${episode.id}${COLORS.RESET}`);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Failed to process episode ${episode.id}: ${error.message}${COLORS.RESET}`);

    const files = fs.readdirSync(TEMP_DOWNLOAD_DIR);
    for (const file of files) {
      if (file.startsWith(`${episode.id}_`)) {
        fs.unlinkSync(path.join(TEMP_DOWNLOAD_DIR, file));
      }
    }
    throw error;
  }
}

async function main() {
  try {
    const { data: episodes, error } = await supabase
      .from('anime')
      .select('*')
      .eq('secondary_source', '{}')
      .order('id', { ascending: false })

    if (error) {
      throw new Error(`Database query failed: ${error.message}`);
    }

    if (!episodes || episodes.length === 0) {
      console.info(`${COLORS.GRAY}[INFO] No episodes requiring processing${COLORS.RESET}`);
      return;
    }

    console.info(`${COLORS.CYAN}[INFO] Found ${episodes.length} episodes to process${COLORS.RESET}`);

    // Process episodes sequentially, but qualities concurrently
    for (const episode of episodes) {
      await processEpisode(episode);
    }

  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Script execution failed: ${error.message}${COLORS.RESET}`);
  } finally {
    if (fs.existsSync(TEMP_DOWNLOAD_DIR)) {
      fs.readdirSync(TEMP_DOWNLOAD_DIR).forEach(file => {
        fs.unlinkSync(path.join(TEMP_DOWNLOAD_DIR, file));
      });
    }
  }
}

await main()