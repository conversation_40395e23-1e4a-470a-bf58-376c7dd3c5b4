// upload.js
import { config } from 'dotenv';
config();
import { createClient } from '@supabase/supabase-js';
import fs from 'fs/promises';
import { createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';
import { randomBytes } from 'crypto';
import { join } from 'path';
import { OpenDriveClient } from '../../openDrive.js';

// Debug mode configuration
const DEBUG_MODE = process.env.DEBUG_MODE === 'true';

const COLORS = {
    RESET: '\x1b[0m',
    RED: '\x1b[31m',
    GREEN: '\x1b[32m',
    CYAN: '\x1b[36m',
    GRAY: '\x1b[90m',
    YELLOW: '\x1b[33m'
};


async function downloadFile(url) {
    // Generate a unique temporary file name in current directory
    const tempFileName = `temp_download_${randomBytes(8).toString('hex')}_${Date.now()}`;
    const tempFilePath = join(process.cwd(), tempFileName);
    
    log('DEBUG', `Downloading to temporary file: ${tempFilePath}`, COLORS.GRAY);
    
    try {
        let apiKey = '61a4b44e-e62b-4c81-a2b8-03a4906c3fb3';
        const response = await fetch(url, {
            headers: {
                Authorization: `Basic ${Buffer.from(`:${apiKey}`).toString('base64')}`
            }
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to download file');
        }

        // Create write stream to temporary file
        const writeStream = createWriteStream(tempFilePath);
        
        // Use pipeline to handle the streaming
        await pipeline(
            response.body,
            writeStream
        );
        
        log('DEBUG', `Download completed to: ${tempFilePath}`, COLORS.GRAY);
        
        return tempFilePath;
    } catch (error) {
        // Clean up temp file if there was an error
        try {
            await fs.unlink(tempFilePath);
        } catch (cleanupError) {
            log('ERROR', `Failed to clean up temporary file: ${cleanupError.message}`, COLORS.RED);
        }
        throw error;
    }
}

async function processEpisodeUpload(config, openDriveClient) {
    const { title, episodeNumber, fileUrls } = config;
    const downloadedFiles = {};

    try {
        for (const [quality, url] of Object.entries(fileUrls)) {
            if (!url) continue;

            const extension = quality === 'source-mkv' ? '.mkv' : '.mp4';
            const fileName = `${title.replace(/ /g, '_')}_${episodeNumber}_${quality}${extension}`;

            let tempFilePath = null;
            try {
                log('INFO', `Processing ${fileName}`, COLORS.CYAN);

                // Download file to temp location in current directory
                log('INFO', 'Downloading source file...', COLORS.CYAN);
                tempFilePath = await downloadFile(url);
                
                // Get file size for logging
                const stats = await fs.stat(tempFilePath);
                log('SUCCESS', `Download completed: ${(stats.size / 1024 / 1024).toFixed(2)}MB`, COLORS.GREEN);

                // Upload to OpenDrive
                const result = await openDriveClient.uploadFile(tempFilePath, fileName);
                log('SUCCESS', `Uploaded ${quality}: ${fileName}`, COLORS.GREEN);
                downloadedFiles[quality] = result.DownloadLink;

            } catch (error) {
                log('ERROR', `Failed to upload or download ${quality}: ${error.message}`, COLORS.RED);
            } finally {
                // Clean up temp file in finally block to ensure cleanup
                if (tempFilePath) {
                    try {
                        await fs.unlink(tempFilePath);
                        log('DEBUG', `Cleaned up temporary file: ${tempFilePath}`, COLORS.GRAY);
                    } catch (cleanupError) {
                        log('ERROR', `Failed to clean up temporary file: ${cleanupError.message}`, COLORS.RED);
                    }
                }
            }

            await new Promise(resolve => setTimeout(resolve, Math.random() * 2500 + 2500));
        }

        await updateEpisode(title, episodeNumber, downloadedFiles);
        log('SUCCESS', `Updated database for ${title} - ${episodeNumber}`, COLORS.GREEN);

    } catch (error) {
        log('ERROR', `Failed to process ${title} - ${episodeNumber}: ${error.message}`, COLORS.RED);
        throw error;
    }
}

async function updateEpisode(animeTitle, episodeNumber, qualityFiles) {
    const supabase = createClient(process.env.SUPABASE_PROJECT_URL, process.env.SUPABASE_ADMIN_KEY);

    try {
        log('DEBUG', `Updating database for ${animeTitle} - ${episodeNumber}`, COLORS.GRAY);
        log('DEBUG', `Quality files: ${JSON.stringify(qualityFiles)}`, COLORS.GRAY);

        const { data: currentAnime, error: fetchError } = await supabase
            .from('anime')
            .select('primary_source')
            .eq('anime_title', animeTitle)
            .eq('episode_number', episodeNumber)
            .single();

        if (fetchError) {
            log('ERROR', `Database fetch error:`, COLORS.RED);
            log('ERROR', JSON.stringify(fetchError), COLORS.RED);
            throw new Error(JSON.stringify(fetchError));
        }
        if (!currentAnime) {
            throw new Error(`Episode not found: ${animeTitle} - ${episodeNumber}`);
        }

        const updatedVideoLinks = { ...currentAnime.primary_source };
        const qualityMap = {
            '1080p': 'FHD',
            '720p': 'HD',
            '480p': 'SD',
            'source': 'Source',
            'source-mkv': 'SourceMKV'
        };

        Object.entries(qualityFiles).forEach(([quality, url]) => {
            const dbQuality = qualityMap[quality];
            if (dbQuality && url) {
                updatedVideoLinks[dbQuality] = url;
                log('DEBUG', `Updated quality ${dbQuality} with URL: ${url}`, COLORS.GRAY);
            }
        });

        const { error: updateError } = await supabase
            .from('anime')
            .update({
                primary_source: updatedVideoLinks,
            })
            .eq('anime_title', animeTitle)
            .eq('episode_number', episodeNumber);

        if (updateError) {
            log('ERROR', `Database update error:`, COLORS.RED);
            log('ERROR', JSON.stringify(updateError), COLORS.RED);
            throw new Error(JSON.stringify(updateError));
        }

        log('DEBUG', `Database update successful`, COLORS.GRAY);
    } catch (error) {
        log('ERROR', `Database operation failed:`, COLORS.RED);
        log('ERROR', error.message, COLORS.RED);
        throw error;
    }
}

// Utility function for logging with debug mode control
function log(type, message, color = COLORS.RESET) {
    const timestamp = new Date().toTimeString().split(' ')[0];

    // Only show DEBUG messages if DEBUG_MODE is enabled
    if (type === 'DEBUG' && !DEBUG_MODE) {
        return;
    }

    console.log(`${color}[${timestamp}] [${type}] ${message}${COLORS.RESET}`);
}

async function main() {
    try {
        log('INFO', 'Starting upload process', COLORS.CYAN);
        log('DEBUG', `Debug mode: ${DEBUG_MODE ? 'enabled' : 'disabled'}`, COLORS.GRAY);

        // Initialize and authenticate OpenDrive client
        const openDrive = new OpenDriveClient();
        log('INFO', 'Authenticating with OpenDrive...', COLORS.CYAN);
        await openDrive.authenticate();
        log('SUCCESS', 'Successfully authenticated with OpenDrive', COLORS.GREEN);

        // Load configuration
        log('INFO', 'Loading configuration...', COLORS.CYAN);
        const configPath = '.re-encode/5upload/config.json';
        const configData = await fs.readFile(configPath, 'utf8');
        const config = JSON.parse(configData);
        log('DEBUG', `Loaded config: ${JSON.stringify(config)}`, COLORS.GRAY);

        // Process uploads
        await processEpisodeUpload(config, openDrive);
        log('SUCCESS', 'All uploads completed successfully', COLORS.GREEN);

    } catch (error) {
        log('ERROR', `Upload process failed: ${error.message}`, COLORS.RED);
        process.exit(1);
    }
}

process.on('unhandledRejection', (error) => {
    log('ERROR', 'Unhandled Promise rejection:', COLORS.RED);
    log('ERROR', error.message, COLORS.RED);
    process.exit(1);
});

main().catch(error => {
    log('ERROR', error.message, COLORS.RED);
    process.exit(1);
});