import { Client } from '@microsoft/microsoft-graph-client';
import 'isomorphic-fetch';
import dotenv from 'dotenv';
import fetch from 'node-fetch';
dotenv.config();
const sharePointUrl2 = `https://lycoriscafe-my.sharepoint.com/personal/lycoriscafe_lycoriscafe_onmicrosoft_com/Documents/mylivewallpapers.com-Telephone-Booth.mp4`
const sharePointUrl3 = `https://lycoriscafe-my.sharepoint.com/personal/lycoriscafe_lycoriscafe_onmicrosoft_com/Documents/%5Blycoris.cafe%5D%20Ao_no_Exorcist_Yosuga-hen%20-%204%20%5B1080p%5D.mp4`
const sharePointUrl = `https://lycoriscafe-my.sharepoint.com/personal/lycoriscafe_lycoriscafe_onmicrosoft_com/Documents/%5Blycoris.cafe%5D%20Ore_dake_Level_Up_na_Ken_Season_2%20-%204%20%5B1080p%5D.mp4`

const credentials = {
  clientId: process.env.CLIENT_ID,
  clientSecret: process.env.CLIENT_SECRET,
  tenantId: process.env.TENANT_ID
};
const getAccessToken = async () => {
  const response = await fetch(
    `https://login.microsoftonline.com/${credentials.tenantId}/oauth2/v2.0/token`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: credentials.clientId,
        client_secret: credentials.clientSecret,
        scope: 'https://graph.microsoft.com/.default'
      })
    }
  );
  const { access_token } = await response.json();
  return access_token;
};
const getAuthenticatedClient = (accessToken) => {
  return Client.init({
    authProvider: (done) => done(null, accessToken)
  });
};
const getFileByPath = async (sharePointUrl) => {
  sharePointUrl = decodeURIComponent(sharePointUrl)
  try {
    const accessToken = await getAccessToken();
    const client = getAuthenticatedClient(accessToken);
    const siteId = 'lycoriscafe-my.sharepoint.com,8ecfe79d-d157-4235-8929-124ef91868bf,ea42ba5c-18e7-4651-8762-a1b45c23400a';
    // Get the filename from the URL
    const fileName = sharePointUrl.split('/').pop();
    const { value: drives } = await client
      .api(`/sites/${siteId}/drives`)
      .get();
    for (const drive of drives) {
      try {
        // Search for the file by its name
        const { value: items } = await client
          .api(`/drives/${drive.id}/root/search(q='${fileName}')`)
          .select('id,name,webUrl')
          .get();
        // Find file by name instead of URL
        const targetFile = items.find(item => item.name === fileName);
        if (targetFile) {
          const response = await fetch(
            `https://graph.microsoft.com/v1.0/drives/${drive.id}/items/${targetFile.id}`,
            {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${accessToken}`
              }
            }
          );
          const data = await response.json();
          if (data['@microsoft.graph.downloadUrl']) {
            console.log(data['@microsoft.graph.downloadUrl']);
            return data['@microsoft.graph.downloadUrl'];
          }
        }
      } catch (error) {
        console.error('Error searching drive:', error);
      }
    }
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};
if (!sharePointUrl) {
  console.error('Please provide a SharePoint URL as an argument');
  process.exit(1);
}
(async () => {
  try {
    await getFileByPath(sharePointUrl);
  } catch (error) {
    console.error('Fatal error:', error);
    process.exit(1);
  }
})();