// @ts-check

/**
 * @file Script for encoding and initializing upload of video files.
 * @description This script encodes an .mkv file (with name of the currently processing file). It transcodes it to .mp4
 * among other enchantments to ensure smooth playback online.
 * @version 1.0
 */

import { config } from 'dotenv';
config();
import { WebhookClient } from 'discord.js';
import axios from 'axios';
import process from 'process';
import fs from 'fs';
import path from 'path';
import cp from 'child_process';
import { existsSync, readFileSync } from 'fs';
// @ts-ignore
import Registry from 'winreg';

const OUTPUT_PATH_FINAL = '5upload/final';
// @ts-ignore
const webhookClient = new WebhookClient({ url: process.env.DISCORD_WEBHOOK_LOGS });
let PREPROCESS_PATH, FINALIZED_PATH;

/**
 * @constant {Object} COLORS - ANSI color codes for console output formatting.
 */
const COLORS = {
  RESET: '\x1b[0m',
  BLACK: '\x1b[30m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
  DIM: '\x1b[2m',
  BG_BLACK: '\x1b[40m',
  BG_RED: '\x1b[41m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
  BG_BLUE: '\x1b[44m',
  BG_MAGENTA: '\x1b[45m',
  BG_CYAN: '\x1b[46m',
  BG_WHITE: '\x1b[47m',
};

/**
 * Main function to process all files in the download directory.
 * @async
 * @function
 * @throws {Error} If an error occurs during file processing.
 */
(async function () {
  try {
    // Read all files from the download directory
    const files = fs.readdirSync(`0download`);

    // Process each file sequentially
    for (const file of files) {
      console.info(`${COLORS.CYAN}[INFO] Starting processing for file: ${file}${COLORS.RESET}`);
      await processFile(file);
    }

    console.info(
      `${COLORS.BG_CYAN}${COLORS.BLACK}[INFO] All files processed. Proceeding to the <UPLOADING> step...${COLORS.RESET}`
    );
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] An error occurred while processing files: ${error.message}${COLORS.RESET}`);
  }
})();

/**
 * Processes a single video file.
 * @async
 * @param {string} file - The name of the video file to process.
 * @returns {Promise<void>}
 * @throws {Error} If an error occurs during processing.
 */
async function processFile(file) {
  // Extract title and episode number from the filename
  let title, episodeNumber;

  // Check if this is a ToonsHub file (case-insensitive)
  if (file.toLowerCase().includes('[toonshub]') ||
      file.toLowerCase().includes('toonshub') ||
      file.match(/\.s\d+e\d+\./i)) {

    // Handle different ToonsHub formats
    let toonsHubMatch;

    // Format 1: [ToonsHub] TITLE S01E03 1080p ...
    toonsHubMatch = file.match(/\[toonshub\]\s*(.+?)\s+s\d+e(\d+)/i);

    // Format 2: Title.Name.S01E03.Episode.Title.REPACK.1080p...ToonsHub
    if (!toonsHubMatch) {
      toonsHubMatch = file.match(/^(.+?)\.s\d+e(\d+)\./i);
      if (toonsHubMatch) {
        // Replace dots with spaces in the title
        toonsHubMatch[1] = toonsHubMatch[1].replace(/\./g, ' ');
      }
    }

    if (!toonsHubMatch) {
      console.error(`${COLORS.RED}[ERROR] Unable to extract title or episode number from ToonsHub filename: ${file}${COLORS.RESET}`);
      return;
    }
    title = toonsHubMatch[1].trim();
    episodeNumber = toonsHubMatch[2];
  } else if (file.toLowerCase().includes('[subsplease]') || file.toLowerCase().includes('subsplease')) {
    // Handle SubsPlease format: [SubsPlease] TITLE - 13 (1080p) [hash].mkv
    const subspleaseMatch = file.match(/\[subsplease\]\s*(.+?)\s-\s(\d+)/i);
    if (!subspleaseMatch) {
      console.error(`${COLORS.RED}[ERROR] Unable to extract title or episode number from SubsPlease filename: ${file}${COLORS.RESET}`);
      return;
    }
    title = subspleaseMatch[1].trim();
    episodeNumber = subspleaseMatch[2];
  } else {
    // Handle Erai-raws format: [Erai-raws] TITLE - 03 [1080p]
    const titleMatch = file.match(/(.+)\s-\s(\d{2})/);
    const episodeNumberMatch = file.match(/.*\s-\s(\d+).*/);
    if (!titleMatch || !episodeNumberMatch) {
      console.error(`${COLORS.RED}[ERROR] Unable to extract title or episode number from Erai-raws filename: ${file}${COLORS.RESET}`);
      return;
    }
    title = titleMatch[1].replace('[Erai-raws] ', '').trim();
    episodeNumber = episodeNumberMatch[1];
  }

  // Set up file paths for processing
  const inputPath = `0download/${file}`;
  PREPROCESS_PATH = `4encode/preprocess_${file}`;
  FINALIZED_PATH = `5upload/final/[lycoris.cafe] ${title} - ${episodeNumber} [source-mkv].mkv`;
  const EN_SUBTITLES_PATH = `5upload/subEN/[lycoris.cafe] ${title} - ${episodeNumber} [EN].ass`;
  const PL_SUBTITLES_PATH = `5upload/subPL/[lycoris.cafe] ${title} - ${episodeNumber}.ass`;

  try {
    // Step 1: Strip all subtitle tracks except the first one
    await stripSubtitleTracksExceptFirst(inputPath, PREPROCESS_PATH);

    // Step 2: Extract the original (usually English) subtitles
    await extractOriginalSubtitles(PREPROCESS_PATH, EN_SUBTITLES_PATH);

    // Step 3: Add Polish subtitles to the MKV file
    await addSubtitlesToMKV(PREPROCESS_PATH, PL_SUBTITLES_PATH, FINALIZED_PATH);

    // Step 4: Transcode the video to various qualities
    // This creates different versions of the video for different streaming qualities
    await transcodeVideo(
      FINALIZED_PATH,
      path.join(OUTPUT_PATH_FINAL, `[lycoris.cafe] ${title} - ${episodeNumber} [1080p].mp4`),
      '1920x1080',
      PL_SUBTITLES_PATH
    );
    await transcodeVideo(
      FINALIZED_PATH,
      path.join(OUTPUT_PATH_FINAL, `[lycoris.cafe] ${title} - ${episodeNumber} [720p].mp4`),
      '1280x720',
      PL_SUBTITLES_PATH
    );
    await transcodeVideo(
      FINALIZED_PATH,
      path.join(OUTPUT_PATH_FINAL, `[lycoris.cafe] ${title} - ${episodeNumber} [480p].mp4`),
      '854x480',
      PL_SUBTITLES_PATH
    );
    const previewPath = `5upload/final/[lycoris.cafe] ${title} - ${episodeNumber} [preview].mp4`;
    await generatePreviewClip(FINALIZED_PATH, previewPath);

    // Clean up: remove temporary files and rename the final MKV
    fs.unlinkSync(PREPROCESS_PATH);
    fs.renameSync(FINALIZED_PATH, `5upload/final/[lycoris.cafe] ${title} - ${episodeNumber} [source-mkv].mkv`);

    console.info(`${COLORS.CYAN}[INFO] Processing completed for: ${file}${COLORS.RESET}`);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error processing file: ${file}${COLORS.RESET}`, error);
    throw error;
  }
}

/**
 * Gets font directories from Windows Registry
 * @async
 * @returns {Promise<string[]>} Array of font directory paths
 */
async function getSystemFontDirs() {
  // @ts-ignore
  const regKey = new Registry({
    // @ts-ignore
    hive: Registry.HKLM,
    key: '\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Fonts'
  });

  const fontDirs = new Set([path.join(process.env.WINDIR || 'C:\\Windows', 'Fonts')]);

  try {
    const items = await new Promise((resolve, reject) => {
      regKey.values((err, items) => {
        if (err) reject(err);
        else resolve(items);
      });
    });

    items.forEach(({ value }) => {
      if (value?.startsWith('C:\\') || value?.startsWith('D:\\')) {
        fontDirs.add(path.dirname(value));
      }
    });

    // Add User Fonts directory (Windows 10+)
    // @ts-ignore
    const userFontsDir = path.join(process.env.LOCALAPPDATA, 'Microsoft', 'Windows', 'Fonts');
    if (existsSync(userFontsDir)) {
      fontDirs.add(userFontsDir);
    }
  } catch (error) {
    console.info(`${COLORS.YELLOW}[WARNING] Could not read registry for additional font locations${COLORS.RESET}`);
  }

  return [...fontDirs];
}

/**
 * Extracts font names from an ASS subtitle file
 * @param {string} subtitlePath - Path to the ASS subtitle file
 * @returns {string[]} Array of font names
 */
function extractFontsFromSubtitle(subtitlePath) {
  const content = readFileSync(subtitlePath, 'utf8');
  const fonts = new Set();

  // Extract fonts from Style section
  const styleSection = content.match(/\[V4\+?\s*Styles\][\s\S]*?(?=\[)/i);
  if (styleSection) {
    // First find the Format line to determine the Fontname column position
    const formatLine = styleSection[0].split('\n').find(line => line.startsWith('Format:'));
    if (formatLine) {
      const columns = formatLine.substring(formatLine.indexOf(':') + 1).split(',').map(col => col.trim());
      const fontnameIndex = columns.findIndex(col => col.toLowerCase() === 'fontname');

      if (fontnameIndex !== -1) {
        // Now process Style lines using the correct column index
        styleSection[0]
          .split('\n')
          .filter(line => line.startsWith('Style:'))
          .forEach(line => {
            const parts = line.substring(line.indexOf(':') + 1).split(',').map(part => part.trim());
            if (parts[fontnameIndex]) {
              fonts.add(parts[fontnameIndex]);
            }
          });
      }
    }
  }

  // Extract fonts from Dialogue sections (inline \fn tags)
  const dialogueLines = content.match(/^Dialogue:.*$/gm);
  if (dialogueLines) {
    dialogueLines.forEach(line => {
      const fnMatches = line.match(/\\fn([^\\}]+)/g);
      if (fnMatches) {
        fnMatches.forEach(match => fonts.add(match.substring(3).trim()));
      }
    });
  }

  return [...fonts];
}

/**
 * Finds a font file in the system
 * @async
 * @param {string} fontName - Name of the font to find
 * @param {string[]} fontDirs - Array of directories to search
 * @returns {Promise<string|null>} Path to the font file or null if not found
 */
async function findFontFile(fontName, fontDirs) {
  const fontExtensions = ['.ttf', '.otf'];

  // Handle special cases for common fonts
  let alternativeName = null;
  if (fontName.toLowerCase() === 'times new roman') {
    alternativeName = 'times';
  } else if (fontName.toLowerCase() === 'comic sans ms') {
    alternativeName = 'comic';
  }

  // Try with original name first
  const fontFile = await tryFindFont(fontName, fontDirs, fontExtensions);
  if (fontFile) return fontFile;

  // If not found and we have an alternative name, try that
  if (alternativeName) {
    const altFontFile = await tryFindFont(alternativeName, fontDirs, fontExtensions);
    if (altFontFile) return altFontFile;
  }

  // If still not found, try replacing spaces with dashes
  const dashedFontName = fontName.replace(/\s+/g, '-');
  if (dashedFontName !== fontName) {
    return tryFindFont(dashedFontName, fontDirs, fontExtensions);
  }

  return null;
}

async function tryFindFont(fontName, fontDirs, fontExtensions) {
  for (const dir of fontDirs) {
    try {
      const files = fs.readdirSync(dir);

      // Try exact match first
      const exactMatch = files.find(file => {
        const ext = path.extname(file).toLowerCase();
        return fontExtensions.includes(ext) &&
          path.basename(file, ext).toLowerCase() === fontName.toLowerCase();
      });

      if (exactMatch) {
        return path.join(dir, exactMatch);
      }

      // Try partial match if exact match not found
      const partialMatch = files.find(file => {
        const ext = path.extname(file).toLowerCase();
        return fontExtensions.includes(ext) &&
          path.basename(file, ext).toLowerCase().includes(fontName.toLowerCase());
      });

      if (partialMatch) {
        return path.join(dir, partialMatch);
      }
    } catch (error) {
      console.info(`${COLORS.YELLOW}[WARNING] Could not read directory ${dir}${COLORS.RESET}`);
    }
  }

  return null;
}

/**
 * Adds subtitles and their font dependencies to an MKV file.
 * @async
 * @param {string} inputPath - The path to the input video file.
 * @param {string} subtitlePath - The path to the subtitle file.
 * @param {string} outputPath - The path for the output video file with subtitles.
 * @returns {Promise<void>}
 */
async function addSubtitlesToMKV(inputPath, subtitlePath, outputPath) {
  console.info(`${COLORS.GRAY}[INFO] Adding subtitles from: ${subtitlePath} to video: ${inputPath}...${COLORS.RESET}`);

  // Extract fonts from subtitle file
  console.info(`${COLORS.GRAY}[INFO] Analyzing subtitle file for fonts...${COLORS.RESET}`);
  const fonts = extractFontsFromSubtitle(subtitlePath);
  console.info(`${COLORS.GRAY}[INFO] Found ${fonts.length} fonts: ${fonts.join(', ')}${COLORS.RESET}`);

  // Get system font directories
  const fontDirs = await getSystemFontDirs();
  console.info(`${COLORS.GRAY}[INFO] Searching for fonts in system directories...${COLORS.RESET}`);

  // Find all font files
  const fontFiles = [];
  const missingFonts = [];

  await Promise.all(fonts.map(async font => {
    const fontFile = await findFontFile(font, fontDirs);
    if (fontFile) {
      fontFiles.push(fontFile);
      console.info(`${COLORS.GRAY}[INFO] Found font file for "${font}": ${fontFile}${COLORS.RESET}`);
    } else {
      missingFonts.push(font);
      console.info(`${COLORS.YELLOW}[WARNING] Could not find font file for "${font}"${COLORS.RESET}`);
    }
  }));

  // Build mkvmerge command
  let command = `mkvmerge -o "${outputPath}" "${inputPath}" --sub-charset 0:UTF-8 --language 0:pl "${subtitlePath}"`;

  // Add font attachments
  fontFiles.forEach(fontFile => {
    const mimeType = path.extname(fontFile).toLowerCase() === '.ttf'
      ? 'application/x-truetype-font'
      : 'application/vnd.ms-opentype';
    command += ` --attachment-mime-type ${mimeType} --attach-file "${fontFile}"`;
  });

  // Execute mkvmerge
  console.info(`${COLORS.GRAY}[INFO] Running mkvmerge...${COLORS.RESET}`);
  await executeCommand(command, []);

  if (missingFonts.length > 0) {
    console.info(`${COLORS.YELLOW}[WARNING] The following fonts were not found in the system:${COLORS.RESET}`);
    missingFonts.forEach(font => console.info(`${COLORS.YELLOW}- ${font}${COLORS.RESET}`));
    console.info(`${COLORS.YELLOW}[WARNING] You may need to install these fonts or add them manually.${COLORS.RESET}`);
  }
}

/**
 * Strips subtitle tracks except the first one from the video file.
 * @async
 * @param {string} inputPath - The path to the input video file.
 * @param {string} outputPath - The path to the output video file.
 * @returns {Promise<void>}
 */
async function stripSubtitleTracksExceptFirst(inputPath, outputPath) {
  console.info(`${COLORS.GRAY}[INFO] Stripping subtitle tracks except the first: ${inputPath}...${COLORS.RESET}`);
  // Use ffmpeg to copy video and audio streams, but only the first subtitle track
  const ffmpegArgs = [
    '-v',
    'quiet',
    '-i',
    `"${inputPath}"`,
    '-map',
    '0:v',
    '-map',
    '0:a',
    '-map',
    '0:s:0',
    '-c',
    'copy',
    `"${outputPath}"`,
    '-y',
  ];
  await executeCommand('ffmpeg', ffmpegArgs);
}

/**
 * Extracts the original subtitles from the video file.
 * @async
 * @param {string} inputPath - The path to the input video file.
 * @param {string} outputPath - The path to the output subtitle file.
 * @returns {Promise<void>}
 */
async function extractOriginalSubtitles(inputPath, outputPath) {
  console.info(`${COLORS.GRAY}[INFO] Extracting original subtitles from: ${inputPath}...${COLORS.RESET}`);
  // Use ffmpeg to extract the first subtitle track
  const ffmpegArgs = ['-v quiet', `-i "${inputPath}"`, '-map 0:s:0', `"${outputPath}"`, '-y'];
  await executeCommand('ffmpeg', ffmpegArgs);
}

/**
 * Transcodes the video file to the specified quality.
 * @async
 * @param {string} inputPath - The path to the input video file.
 * @param {string} outputPath - The path to the output video file.
 * @param {string} quality - The quality of the transcoded video (e.g., "1280x720", "source").
 * @param {string} PLSubtitles - The path to the Polish subtitle file.
 * @returns {Promise<void>}
 * @throws {Error} If an invalid resolution is provided.
 */
async function transcodeVideo(inputPath, outputPath, quality, PLSubtitles) {
  // Copy subtitles to a temporary file for ffmpeg processing
  let oldSubFile = PLSubtitles;
  fs.copyFileSync(PLSubtitles, 'sub.ass');
  let tmpSubFile = 'sub.ass';
  console.info(`[INFO] Transcoding to ${quality}...`);

  let ffmpegArgs;
  // Set up ffmpeg arguments based on the desired quality
  switch (quality) {
    case 'source':
      // For source quality, we maintain the original resolution but re-encode for web compatibility
      ffmpegArgs = [
        '-v',
        'info',
        '-hide_banner',
        '-stats',
        `-i "${inputPath}"`,
        `-vf`,
        `ass=${tmpSubFile}`,
        '-c:v',
        'h264_nvenc',
        '-rc',
        'vbr',
        '-cq',
        '18',
        '-bf:v',
        '4',
        '-rc-lookahead:v',
        '35',
        '-map',
        '0:v',
        '-map',
        '0:a',
        '-tune',
        'hq',
        '-preset',
        'slow',
        '-pix_fmt',
        'yuv420p',
        '-c:a',
        'aac',
        '-b:a',
        '256k',
        '-movflags',
        '+faststart',
        `"${outputPath}"`,
        '-y',
      ];
      break;

    case '1920x1080':
    case '1280x720':
    case '854x480':
      // For other qualities, we scale the video and adjust encoding parameters
      ffmpegArgs = [
        '-v',
        'info',
        '-hide_banner',
        '-stats',
        `-i "${inputPath}"`,
        `-vf`,
        `ass="${tmpSubFile}",scale=${quality}`,
        '-c:v',
        'h264_nvenc',
        '-rc',
        'vbr',
        '-cq',
        quality === '854x480' ? '23' : '25', // Adjust quality based on resolution
        '-bf:v',
        '4',
        '-rc-lookahead:v',
        '35',
        '-map',
        '0:v',
        '-map',
        '0:a',
        '-tune',
        'hq',
        '-preset',
        'slow',
        '-pix_fmt',
        'yuv420p',
        '-c:a',
        'aac',
        '-b:a',
        '256k',
        '-movflags',
        '+faststart',
        `"${outputPath}"`,
        '-y',
      ];
      break;

    default:
      throw new Error(`Invalid resolution ${quality}`);
  }

  // Execute the ffmpeg command
  await executeCommand('ffmpeg', ffmpegArgs);
  console.info(`[INFO] Transcoding to ${quality} completed.`);

  // Log completion to Discord
  await webhookClient.send({
    content: `[INFO] Transcoding to ${quality} completed.`,
  });

  // Clean up temporary subtitle file
  fs.unlinkSync('sub.ass');
}

async function generatePreviewClip(inputPath, outputPath) {
  console.info(`${COLORS.GRAY}[INFO] Generating 30-second preview clip...${COLORS.RESET}`);

  const ffmpegArgs = [
    '-ss', '00:01:35',  // Start time
    '-t', '30',         // Duration
    '-i', `"${inputPath}"`,
    '-vf', 'scale=854x480', // 480p resolution
    '-c:v', 'h264_nvenc',
    '-preset', 'slow',
    '-rc', 'vbr',
    '-cq', '23',
    '-b:v', '1M',
    '-c:a', 'aac',
    '-b:a', '128k',
    `"${outputPath}"`,
    '-y'
  ];

  await executeCommand('ffmpeg', ffmpegArgs);
  console.info(`${COLORS.CYAN}[INFO] Preview clip generated successfully${COLORS.RESET}`);
}

/**
 * Executes a command with the specified arguments.
 * @async
 * @param {string} command - The command to execute.
 * @param {string[]} args - The arguments to pass to the command.
 * @returns {Promise<void>}
 * @throws {Error} If the process exits with a non-zero code.
 */
async function executeCommand(command, args) {
  return new Promise((resolve, reject) => {
    // Spawn a child process to execute the command
    const process = cp.spawn(command, args, { windowsHide: true, shell: true });
    console.log(args.join(' '));

    // Log stdout and stderr
    process.stdout.on('data', (data) => console.log(data.toString()));
    process.stderr.on('data', (data) => console.log(data.toString()));

    // Resolve or reject the promise based on the exit code
    process.on('exit', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Process exited with code ${code}`));
      }
    });
  });
}
