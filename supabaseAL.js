import { config } from 'dotenv';
config();

import { createClient } from '@supabase/supabase-js'
import fetch from 'node-fetch'

const supabase = createClient(
  process.env.SUPABASE_PROJECT_URL,
  process.env.SUPABASE_ADMIN_KEY
)

const ANILIST_API = 'https://graphql.anilist.co'

// Define delay range (in milliseconds)
const MIN_DELAY = 0  // 2 seconds
const MAX_DELAY = 5000  // 5 seconds

const getRandomDelay = () =>
  Math.floor(Math.random() * (MAX_DELAY - MIN_DELAY + 1) + MIN_DELAY)

const searchQuery = `
query ($search: String) {
  Media(search: $search, type: ANIME) {
    id
    title {
      romaji
    }
  }
}`

const searchAniList = async (title) => {
  try {
    const response = await fetch(ANILIST_API, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: searchQuery,
        variables: { search: title },
      }),
    })

    const { data } = await response.json()
    return data.Media?.id ?? null
  } catch (error) {
    console.error(`Error searching for "${title}":`, error)
    return null
  }
}

const markAsSkipped = async (animeId) => {
  try {
    const { error } = await supabase
      .from('anime')
      .update({ anilist_id: -1 })
      .eq('id', animeId)

    if (error) {
      console.error('Error marking entry as skipped:', error)
    }
  } catch (error) {
    console.error('Error in markAsSkipped:', error)
  }
}

const updateAnime = async () => {
  try {
    const { data: anime, error } = await supabase
      .from('anime')
      .select('*')
      .is('anilist_id', null)
      .limit(1)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        console.log('No more anime to process')
        return false
      }
      throw error
    }

    const searchTitle = anime.anime_title.replace(/_/g, ' ')
    console.log(`Searching for: ${searchTitle} - ${anime.episode_number}`)

    const anilistId = await searchAniList(searchTitle)

    if (anilistId) {
      const { error: updateError } = await supabase
        .from('anime')
        .update({ anilist_id: anilistId })
        .eq('id', anime.id)

      if (updateError) {
        console.error(`Error updating anime ${searchTitle}:`, updateError)
        await markAsSkipped(anime.id)
        return true
      }

      console.log(`Updated ${searchTitle} with AniList ID: ${anilistId}`)
    } else {
      console.log(`No match found for: ${searchTitle}, marking as skipped`)
      await markAsSkipped(anime.id)
    }

    return true

  } catch (error) {
    console.error('Error processing anime:', error)
    return true
  }
}

const main = async () => {
  try {
    console.log('Starting anime search...')

    let hasMore = true
    while (hasMore) {
      hasMore = await updateAnime()
      if (hasMore) {
        const delay = getRandomDelay()
        console.log(`Waiting ${delay / 1000} seconds before next request...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    console.log('Finished processing anime list')
  } catch (error) {
    console.error('Script failed:', error)
  } finally {
    process.exit(0)
  }
}

main()