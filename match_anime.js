import fetch from 'node-fetch';
import { writeFileSync } from 'fs';

const titles = {
  'Tensei shitara Slime Datta Ken 3rd Season': '<@&1232127675913469982>',
  '<PERSON><PERSON><PERSON> to <PERSON><PERSON><PERSON><PERSON><PERSON> Merchant Meets the Wise Wolf': '<@&1232073618993840218>',
  '<PERSON><PERSON> no Ko 2nd Season': '<@&1258118614217920594>',
  Roshidere: '<@&1258118476288102614>',
  'Isekai Suicide Squad': '<@&1255874772168867840>',
  'Gimai Seikatsu': '<@&1258417007653687419>',
  'Giji Harem': '<@&1258491152173236254>',
  '<PERSON><PERSON><PERSON> wa <PERSON>tok<PERSON>ko': '<@&1258506878825140285>',
  '2.5-Jigen no Ririsa': '<@&1258778773336096849>',
  '2.5-jigen_no_Ririsa': '<@&1258778773336096849>',
  'NieR Automata Ver1.1a Part 2': '<@&1258838444486955008>',
  'Shoushimin Series': '<@&1259237966916882433>',
  'Mayonaka Punch': '<@&1259249609105211473>',
  'Koi wa Futago de Warikirenai': '<@&1259249651660619839>',
  'Atri My Dear Moments': '<@&1259249763765846096>',
  'Make Heroine ga Oosugiru': '<@&1259249922700607720>',
  'Nanare Hananare': '<@&1259249973124665427>',
  'Kami no Tou 2nd Season': '<@&1259264562113544192>',
  'Shikanoko Nokonoko Koshitantan': '<@&1258371053692911667>',
  'VTuber Nandaga Haishin Kiri Wasuretara Densetsu ni Natteta': '<@&1259264469398196275>',
  'Mushoku Tensei II Isekai Ittara Honki Dasu Part 2': '<@&1232063792192426086>',
  'Kono Subarashii Sekai ni Shukufuku wo 3': '<@&1232127754401742848>',
  'Kaijuu 8 Gou': '<@&1232127795203801158>',
  'Lv2 kara Cheat datta Moto Yuusha Kouho no Mattari Isekai Life': '<@&1232127877487661137>',
  Dainanaoji: '<@&1232063922799116368>',
  Madome: '<@&1232093911422341221>',
  'Re-Monster': '<@&1232063838279438449>',
  'Tensei Kizoku Kantei Skill de Nariagaru': '<@&1232132038610653348>',
  'Date a Live V': '<@&1232128136058241155>',
  'Unnamed Memory Act 2': '<@&1320564769949876296>',
  'Kami wa Game ni Uete Iru': '<@&1232063952813293618>',
  'Yuru Camp Season 3': '<@&1232073472356651091>',
  'Bartender Kami no Glass': '<@&1232073397341655050>',
  'Kaii to Otome to Kamikakushi': '<@&1232128365759303721>',
  'Henjin no Salad Bowl  ': '<@&1232073592024600627>',
  'Shuumatsu Train Doko e Iku': '<@&1232063872043716669>',
  'Seiyuu Radio no Uraomote': '<@&1232128493442175099>',
  'Monogatari Series Off and Monster Season': '<@&1288172249639026770>',
  'Monogatari Series Off & Monster Season': '<@&1288172249639026770>',
  'Monogatari Series Off & Monster Season A Cruel Fairy Tale The Beautiful Princess': '<@&1288172249639026770>',
  'Maou-sama Retry R': '<@&1289597054850437241>',
  Uzumaki: '<@&1289614862057607268>',
  'Re Zero kara Hajimeru Isekai Seikatsu 3rd Season': '<@&1289597034596401285>',
  danmachi5: '<@&1289597041177133057>',
  'Dan Da Dan': '<@&1289597048559243388>',
  'Rurouni Kenshin Kyoto Douran': '<@&1289597057119686687>',
  'NegaPosi Angler': '<@&1289598946871738559>',
  'Sword Art Online Alternative Gun Gale Online II': '<@&1289597051088142452>',
  'Kimi wa Meido sama': '<@&1289598890458484746>',
  'Blue Lock vs U 20 Japan': '<@&1289597046835249253>',
  'MF Ghost 2nd Season': '<@&1289600626120196139>',
  'Kamonohashi Ron no Kindan Suiri 2nd Season': '<@&1289597055920115712>',
  'Seirei Gensouki 2nd Season': '<@&1289597052858269726>',
  'Maou 2099': '<@&1290742367158276260>',
  'Ao no Exorcist Yuki no Hate Hen': '<@&1293220192486293577>',
  'Rekishi ni Nokoru Akujo ni Naruzo': '<@&1292612200904523889>',
  'Shangri-La Frontier S2': '<@&1289597044364673054>',
  'Arifureta Shokugyou de Sekai Saikyou 3rd Season': '<@&1289597038702362706>',
  'Maou 2099': '<@&1290742367158276260>',
  'Houkago Shounen Hanako kun Zoku hen': '<@&1289597058965307548>',
  Wajutsushi: '<@&1304516888881135636>',
  'Amagami-san Chi no Enmusubi': '<@&1304516969583607895>',
  'Nageki no Bourei wa Intai Shitai': '<@&1304517019760197653>',
  'Trillion Game': '<@&1305578289179525141>',
  'Tsuma Shougakusei ni Naru': '<@&1305578351242772490>',
  'Mecha-ude TV': '<@&1305578440732442754>',
  'Hitoribocchi no Isekai Kouryaku': '<@&1305578559129256068>',
  'Gotoubun no Hanayome＊': '<@&1320564763146588211>',
  'Grisaia - Phantom Trigger': '<@&1320561705578921985>',
  'Grisaia Phantom Trigger': '<@&1320561705578921985>',
  'Ameku Takao no Suiri Karte': '<@&1320561694400974971>',
  'Momentary Lily': '<@&1320561688961093632>',
  'Class no Daikirai na Joshi to Kekkon suru Koto ni Natta': '<@&1320561723274694777>',
  'Ore dake Level Up na Ken Season 2': '<@&1320561713149640839>',
  'Ao no Exorcist  Yosuga-hen': '<@&1320561716286849195>',
  'Ao no Exorcist Yosuga-hen': '<@&1320561716286849195>',
  'Zenshuu': '<@&1320561698716782622>',
  'Kono Kaisha ni Suki na Hito ga Imasu': '<@&1320561733395284018>',
  'Kuroiwa Medaka ni Watashi no Kawaii ga Tsuujinai': '<@&1320561740265820160>',
  'Honey Lemon Soda': '<@&1320561750395064412>',
  'Dr. STONE SCIENCE FUTURE': '<@&1320561730048229410>',
  'Dr Stone - Science Future': '<@&1320561730048229410>',
  'Arafo Otoko No Isekai Tsuhan Seikatsu': '<@&1320561701677961289>',
  'Nihon e Youkoso Elf-san': '<@&1320563696971550820>',
  'Kusuriya no Hitorigoto 2nd Season': '<@&1320561726760026208>',
  'Girumasu': '<@&1320564767274045542>',
  'Jibaku Shounen Hanako-kun 2': '<@&1320561709395611679>',
  'Kimi no Koto ga Daidaidaidaidaisuki na 100-nin no Kanojo 2nd Season': '<@&1320561719612801126>',
};

const query = `
query ($search: String) {
  Media(search: $search, type: ANIME) {
    id
    title {
      romaji
      english
    }
  }
}
`;

const getAniListId = async (title) => {
  const variables = {
    search: title
  };

  const response = await fetch('https://graphql.anilist.co', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: JSON.stringify({
      query,
      variables
    })
  });

  const data = await response.json();
  return data.data?.Media?.id ?? null;
};

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const matchAllTitles = async () => {
  const result = {};

  for (const [title, roleId] of Object.entries(titles)) {
    try {
      await sleep(2000); // Respect rate limits

      const anilistId = await getAniListId(title);
      result[title] = {
        roleId,
        anilistId
      };

      console.log(`Processed: ${title} -> ${anilistId}`);
    } catch (error) {
      console.error(`Error processing ${title}:`, error);
      result[title] = {
        roleId,
        anilistId: null
      };
    }
  }

  writeFileSync('anime_mappings.json', JSON.stringify(result, null, 2));
  console.log('Results written to anime_mappings.json');
};

matchAllTitles();