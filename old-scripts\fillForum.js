import { config } from 'dotenv';
config();

import fs from 'fs';
import { WebhookClient, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_PROJECT_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
const webhookUrl = process.env.FORUM_DISCORD_WEBHOOK;

// ============ CONFIGURATION OPTIONS ============
// Set to true to only fetch anime added after the cutoff date
const FETCH_ONLY_NEWER_ANIME = true;
// Cutoff date for fetching newer anime
const DATE_CUTOFF = '2025-04-29 20:51:31.808443+00';
// =============================================

// Load role pings and forum posts from JSON files
let rolePings;
let forumPosts;

try {
  const rolePingsPath = './_resources/role_pings.json';
  const rolePingsData = fs.readFileSync(rolePingsPath, 'utf8');
  rolePings = JSON.parse(rolePingsData);
  console.log('Role pings loaded successfully');
} catch (error) {
  console.error(`Error loading role_pings.json: ${error}`);
  rolePings = {};
}

try {
  const forumPostsPath = './_resources/forum_posts.json';
  const forumPostsData = fs.readFileSync(forumPostsPath, 'utf8');
  forumPosts = JSON.parse(forumPostsData);
  console.log('Forum posts loaded successfully');
} catch (error) {
  console.error(`Error loading forum_posts.json: ${error}`);
  forumPosts = {};
}

// Get anime metadata based on configuration settings
async function getAnimeMetadata() {
  let query = supabase
    .from('anime_metadata')
    .select('anilist_id, romaji_title, hidden');
  
  if (FETCH_ONLY_NEWER_ANIME) {
    console.log(`Fetching only anime added on or after ${DATE_CUTOFF}`);
    
    // Get only the anilist_ids of anime added after the cutoff date
    let { data: newerAnimeIds, error: newerIdsError } = await supabase
      .from('anime')
      .select('anilist_id')
      .gte('date_added', DATE_CUTOFF)
      .order('date_added', { ascending: true });
    
    if (newerIdsError) {
      console.error(`Error querying newer anime IDs: ${newerIdsError}`);
      process.exit(1);
    }
    
    // Extract unique anilist_ids
    const uniqueIds = [...new Set(newerAnimeIds.map(item => item.anilist_id))];
    console.log(`Found ${uniqueIds.length} unique anime IDs added after cutoff date`);
    
    // Only fetch metadata for these anime IDs
    query = query.in('anilist_id', uniqueIds);
  }
  
  let { data, error } = await query;
  
  if (error) {
    console.error(`Error querying anime metadata: ${error}`);
    process.exit(1);
  }
  
  return data;
}

// Get filtered anime metadata
const animeMetadata = await getAnimeMetadata();

// Filter out hidden anime and those without forum posts
const animeToProcess = animeMetadata
  .filter(anime => !anime.hidden)
  .filter(anime => getForumPostId(anime.anilist_id))
  .sort((a, b) => a.romaji_title.localeCompare(b.romaji_title));

console.log(`Found ${animeToProcess.length} anime series to process`);

// Process each anime series
for (let i = 0; i < animeToProcess.length; i++) {
  const animeSeries = animeToProcess[i];
  const forumPostId = getForumPostId(animeSeries.anilist_id);
  
  console.log(`\n[${i+1}/${animeToProcess.length}] Processing anime: ${animeSeries.romaji_title} (ID: ${animeSeries.anilist_id})`);
  console.log(`Forum post ID: ${forumPostId}`);
  
  // Get all episodes for this anime series
  let episodesQuery = supabase
    .from('anime')
    .select('id, anime_title, episode_number, thumbnail_link, anilist_id')
    .eq('anilist_id', animeSeries.anilist_id);
  
  // Apply date filter if only fetching newer anime
  if (FETCH_ONLY_NEWER_ANIME) {
    episodesQuery = episodesQuery.gte('date_added', DATE_CUTOFF);
  }
  
  // Order by episode number
  episodesQuery = episodesQuery.order('episode_number', { ascending: true });
  
  let { data: episodes, error: episodesError } = await episodesQuery;
  
  if (episodesError) {
    console.error(`Error fetching episodes for anime ID ${animeSeries.anilist_id}: ${episodesError}`);
    continue;
  }
  
  console.log(`Found ${episodes.length} episodes for ${animeSeries.romaji_title}`);
  
  // Process each episode for this anime
  for (let j = 0; j < episodes.length; j++) {
    const episode = episodes[j];
    const episodeEmoji = numberToDiscordEmoji(episode.episode_number.toString().padStart(2, '0'));
    const rolePing = idToRolePing(episode.anilist_id);
    
    console.log(`  - Processing Episode ${episode.episode_number}...`);
    
    await publishToForumThread(
      episode.anilist_id,
      episode.anime_title, 
      episodeEmoji, 
      rolePing, 
      episode.thumbnail_link, 
      episode.episode_number, 
      forumPostId
    );
    
    // Sleep longer between episodes of the same anime to avoid rate limits
    await sleep(3000);
  }
  
  // Sleep between different anime series to avoid rate limits
  console.log(`Completed all episodes for ${animeSeries.romaji_title}. Waiting before processing next anime...`);
  await sleep(5000);
}

console.log("All anime series have been processed!");

async function publishToForumThread(
  animeId,
  animeTitle,
  episodeEmoji,
  rolePing,
  thumbnailUrl,
  episodeNumber,
  forumPostId
) {
  const webhookClient = new WebhookClient({ url: webhookUrl });

  let animeTitleLink = animeTitle.toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

  const embed = new EmbedBuilder()
    .setTitle(`${episodeEmoji} ${animeTitle.replaceAll('_', ' ')}`)
    .setDescription(
      `**Obejrzyj online: [[KLIKNIJ TUTAJ!]](https://www.lycoris.cafe/anime/${animeId}/${animeTitleLink}/watch/${Number(episodeNumber)}) \nPobierz odcinek: [PRZYCISKI PONIŻEJ]**`
    )
    .setImage(thumbnailUrl);

  // Create buttons using animeId instead of title
  const button1 = new ButtonBuilder()
    .setLabel('MKV')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=mkv`)
    .setStyle(ButtonStyle.Link);
  const button2 = new ButtonBuilder()
    .setLabel('1080p')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=1080p`)
    .setStyle(ButtonStyle.Link);
  const button3 = new ButtonBuilder()
    .setLabel('720p')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=720p`)
    .setStyle(ButtonStyle.Link);
  const button4 = new ButtonBuilder()
    .setLabel('480p')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=480p`)
    .setStyle(ButtonStyle.Link);
  const button5 = new ButtonBuilder()
    .setLabel('Napisy PL')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=pl`)
    .setStyle(ButtonStyle.Link);
  const row = new ActionRowBuilder().addComponents(button1, button2, button3, button4, button5);

  const messageOptions = {
    embeds: [embed],
    components: [row],
    allowed_mentions: {
      parse: ['everyone', 'users'],
    },
    threadId: forumPostId // Always include threadId for forum posts
  };

  try {
    await webhookClient.send(messageOptions);
    console.log(`  ✓ Message sent to forum thread for Episode ${episodeNumber}`);
  } catch (error) {
    console.error(`  ✗ Error sending webhook for Episode ${episodeNumber}: ${error}`);
  }
}

function numberToDiscordEmoji(num) {
  const emojiMap = {
    0: ':zero: ',
    1: ':one: ',
    2: ':two: ',
    3: ':three: ',
    4: ':four: ',
    5: ':five: ',
    6: ':six: ',
    7: ':seven: ',
    8: ':eight: ',
    9: ':nine: ',
  };

  return String(num)
    .split('')
    .map((digit) => emojiMap[digit])
    .join('');
}

function idToRolePing(anilistId) {
  if (!anilistId || !rolePings) return null;
  
  // Convert anilistId to string for JSON key lookup
  const id = String(anilistId);
  return rolePings[id] || null;
}

function getForumPostId(anilistId) {
  if (!anilistId || !forumPosts) return null;
  
  // Convert anilistId to string for JSON key lookup
  const id = String(anilistId);
  return forumPosts[id] || null;
}

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}