import { config } from 'dotenv';
config();
import fs from 'fs/promises';

import { SlashCommandBuilder } from '@discordjs/builders';
import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_PROJECT_URL;
const supabaseKey = process.env.SUPABASE_ADMIN_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

export const data = new SlashCommandBuilder()
    .setName('createseasonalroles')
    .setDescription('Creates roles for seasonal anime and generates a role mapping JSON file');

export async function run({ interaction, client, handler }) {
    // User authentication
    if (interaction.user.id !== '351006685587963916' && interaction.user.id !== '185126303677153281') {
        await interaction.reply({
            content: '<PERSON><PERSON> mo<PERSON> tej komendy.',
            ephemeral: true,
        });
        return;
    }

    await interaction.deferReply();
    await interaction.editReply('Fetching seasonal anime from database...');

    try {
        //CHANGE: season and year here as season changes.
        const { data: animeList, error } = await supabase
            .from('anime_metadata')
            .select('anilist_id, romaji_title, synonyms')
            .eq('season', 'SUMMER')
            .eq('season_year', 2025);

        if (error) {
            throw new Error(`Supabase query error: ${error.message}`);
        }

        if (!animeList || animeList.length === 0) {
            await interaction.editReply('No seasonal anime found in the database.');
            return;
        }

        await interaction.editReply(`Found ${animeList.length} seasonal anime. Creating roles...`);

        const roleMappings = {};
        const forumPostMappings = {};
        const titleMappings = {};
        let createdCount = 0;
        let skippedCount = 0;

        // Load existing resource files
        let existingForumPosts = {};
        let existingRolePings = {};
        let existingTitleMappings = {};

        try {
            const forumPostsData = await fs.readFile('./_resources/forum_posts.json', 'utf8');
            existingForumPosts = JSON.parse(forumPostsData);
        } catch (error) {
            console.log('Could not load existing forum_posts.json, starting fresh');
        }

        try {
            const rolePingsData = await fs.readFile('./_resources/role_pings.json', 'utf8');
            existingRolePings = JSON.parse(rolePingsData);
        } catch (error) {
            console.log('Could not load existing role_pings.json, starting fresh');
        }

        try {
            const titleMappingsData = await fs.readFile('./_resources/title_mappings.json', 'utf8');
            existingTitleMappings = JSON.parse(titleMappingsData);
        } catch (error) {
            console.log('Could not load existing title_mappings.json, starting fresh');
        }

        // Process each anime and create roles
        for (const anime of animeList) {
            try {
                // Determine the role name (prioritize synonyms if available)
                let roleName = '';

                if (anime.synonyms && anime.synonyms.length > 0) {
                    // Find the shortest synonym that's not too short
                    const validSynonyms = anime.synonyms.filter(s => s && s.length >= 3 && s.length <= 100);
                    if (validSynonyms.length > 0) {
                        roleName = validSynonyms.reduce((shortest, current) =>
                            (current.length < shortest.length) ? current : shortest
                        );
                    }
                }

                // If no valid synonym was found, use romaji_title
                if (!roleName) {
                    roleName = anime.romaji_title;

                    // Truncate long role names to fit Discord's 100-character limit
                    if (roleName.length > 100) {
                        roleName = roleName.substring(0, 97) + '...';
                    }
                }

                // Check if role already exists
                const existingRole = interaction.guild.roles.cache.find(role => role.name === roleName);
                if (existingRole) {
                    roleMappings[anime.anilist_id] = `<@&${existingRole.id}>`;
                    skippedCount++;
                } else {
                    // Create the role
                    const newRole = await interaction.guild.roles.create({
                        name: roleName,
                        mentionable: true,
                        reason: 'seasonal anime role'
                    });
                    // Add to mappings
                    roleMappings[anime.anilist_id] = `<@&${newRole.id}>`;
                    createdCount++;
                }

                // Add to forum posts mapping (with placeholder for forum post ID)
                if (!existingForumPosts[anime.anilist_id]) {
                    forumPostMappings[anime.anilist_id] = "PLACEHOLDER_FORUM_POST_ID";
                }

                // Add to title mappings
                if (!existingTitleMappings[roleName]) {
                    titleMappings[roleName] = anime.anilist_id;
                }

                // Update progress occasionally
                if (createdCount % 5 === 0 || createdCount + skippedCount === animeList.length) {
                    await interaction.editReply(
                        `Processing seasonal anime roles... ` +
                        `\nProgress: ${createdCount + skippedCount}/${animeList.length} ` +
                        `\nCreated: ${createdCount} | Skipped: ${skippedCount}`
                    );
                }

                // Add a small delay to avoid rate limits
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (roleError) {
                console.error(`Error creating role for ${anime.romaji_title}:`, roleError);
            }
        }

        // Merge new mappings with existing ones
        const updatedForumPosts = { ...existingForumPosts, ...forumPostMappings };
        const updatedRolePings = { ...existingRolePings, ...roleMappings };
        const updatedTitleMappings = { ...existingTitleMappings, ...titleMappings };

        // Save all resource files
        await fs.writeFile(
            './_resources/seasonal_role_pings.json',
            JSON.stringify(roleMappings, null, 2)
        );

        await fs.writeFile(
            './_resources/forum_posts.json',
            JSON.stringify(updatedForumPosts, null, 2)
        );

        await fs.writeFile(
            './_resources/role_pings.json',
            JSON.stringify(updatedRolePings, null, 2)
        );

        await fs.writeFile(
            './_resources/title_mappings.json',
            JSON.stringify(updatedTitleMappings, null, 2)
        );

        await interaction.editReply(
            `Completed creating seasonal anime roles!\n` +
            `Total anime: ${animeList.length}\n` +
            `Created roles: ${createdCount}\n` +
            `Skipped (already exist): ${skippedCount}\n` +
            `Resource files updated:\n` +
            `- seasonal_role_pings.json (seasonal roles only)\n` +
            `- role_pings.json (merged with existing)\n` +
            `- forum_posts.json (added placeholders for new anime)\n` +
            `- title_mappings.json (added new title mappings)`
        );
    } catch (error) {
        console.error('Error processing seasonal anime roles:', error);
        await interaction.editReply(`An error occurred: ${error.message}`);
    }
}

/**
 * @type {Object}
 * @property {boolean} devOnly - Indicates if the command is for developers only
 * @property {boolean} deleted - Indicates if the command has been deleted
 */
export const options = {
    devOnly: true,
    deleted: false,
};