// src/lib/server/rateLimit.js
class RateLimit {
  constructor(limit = 90, intervalSeconds = 60) {
    this.requests = 0;
    this.lastReset = Date.now();
    this.limit = limit;
    this.interval = intervalSeconds * 1000;
  }

  async waitForSlot() {
    const now = Date.now();
    if (now - this.lastReset > this.interval) {
      this.requests = 0;
      this.lastReset = now;
    }

    if (this.requests >= this.limit) {
      const waitTime = this.interval - (now - this.lastReset);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      return this.waitForSlot();
    }

    this.requests++;
  }
}

export const anilistRateLimit = new RateLimit();