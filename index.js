/**
 * The main entry point for lanuching the discord bot
 * node index.js
 */

import 'dotenv/config';
import { Client, GatewayIntentBits } from 'discord.js';
import { CommandKit } from 'commandkit';
import path from 'path';
import { fileURLToPath } from 'url';

const botToken = process.env.BOT_TOKEN;

const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
  ],
});

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

new CommandKit({
  client,
  commandsPath: path.join(__dirname, 'commands'),
  eventsPath: path.join(__dirname, 'events'),
  validationsPath: path.join(__dirname, 'validations'),
  devGuildIds: ['1367968408057942189'],
  devUserIds: ['351006685587963916'],
  skipBuiltInValidations: true,
  bulkRegister: true,
});

client.login(botToken);
