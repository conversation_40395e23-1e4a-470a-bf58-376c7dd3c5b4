/**
 * @file Discord Anime Bot
 * @description This script manages the process of publishing new anime episode information to Discord channels and other platforms. It performs the following tasks:
 *
 * 1. Environment Setup:
 *    - Loads environment variables for various API keys and URLs.
 *    - Initializes connections to Supabase and Discord.
 *
 * 2. Anime Episode Data Retrieval:
 *    - Accepts anime title and episode number as command-line arguments.
 *    - Queries the Supabase database for detailed information about the specified anime episode.
 *
 * 3. Data Preparation:
 *    - Extracts relevant information from the database query results.
 *    - Generates Discord emoji representation of the episode number.
 *    - Determines the appropriate Discord role to ping for the anime.
 *
 * 4. Docchi Integration:
 *    - Maps the anime title to its corresponding title in the Docchi system.
 *    - Creates a new episode entry in the Docchi system with relevant information.
 *
 * 5. Discord Publication:
 *    - Publishes the episode information to a public Discord channel.
 *    - Creates an embedded message with:
 *      - Anime title and episode number.
 *      - Link to watch the episode online.
 *      - Buttons for downloading different quality versions and subtitles.
 *    - Pings the relevant role to notify subscribers.
 *
 * 6. Developer Channel Update:
 *    - Sends an iframe embed code to a development Discord channel for testing purposes.
 *
 * 7. Forum Channel Publication:
 *    - Determines the appropriate forum thread ID for the anime.
 *    - Posts the episode information in the corresponding forum thread.
 *
 * 8. Error Handling:
 *    - Implements error checking and logging throughout the process.
 *
 * 9. Utility Functions:
 *    - Includes helper functions for:
 *      - Converting numbers to Discord emoji strings.
 *      - Mapping anime titles to role IDs and forum post IDs.
 *      - Translating anime titles for different systems (e.g., Docchi).
 *
 * This script serves as a comprehensive tool for automating the announcement and distribution
 * of new anime episodes across multiple platforms, enhancing user engagement and providing
 * easy access to episode information and download options.
 */

import rolePings from './_resources/role_pings.json' assert { type: 'json' };;
import docchiTitleFixes from './_resources/docchi_title_fixes.json' assert { type: 'json' };;
import forumPosts from './_resources/forum_posts.json' assert { type: 'json' };;
import { config } from 'dotenv';
config();

import axios from 'axios';
import { WebhookClient, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';
import { createClient } from '@supabase/supabase-js';

const webhookClient = new WebhookClient({ url: process.env.DISCORD_WEBHOOK_LOGS });
// Environment variables and configurations
const supabaseUrl = process.env.SUPABASE_PROJECT_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
const publicWebhookUrl = process.env.PUBLIC_DISCORD_WEBHOOK;
const devWebhookUrl = process.env.DEV_DISCORD_WEBHOOK;
const forumWebhookUrl = process.env.FORUM_DISCORD_WEBHOOK;
const docchiAuthKey = process.env.DOCCHI_AUTH_KEY;
const docchiClientID = process.env.DOCCHI_CLIENT_ID;

const animeId = process.argv[2];
const episodeNumber = process.argv[3];

/**
 * Fetches anime information from the database.
 * @async
 * @throws {Error} If no results are found for the given anime title and episode number.
 */
// Fetch anime information from the database.
let { data: animeQuery, error } = await supabase
  .from('anime')
  .select('id, primary_source, season, thumbnail_link, subtitleLinks, markerPeriods, thumbnailFile, anime_title')
  .eq('anilist_id', animeId)
  .eq('episode_number', Number(episodeNumber));

// Ensure we have data to work with
if (!animeQuery || animeQuery.length === 0) {
  throw new Error(
    `Query: "SELECT primary_source, season, thumbnail_link, subtitleLinks, markerPeriods, thumbnailFile FROM anime WHERE anilist_id = '${animeId}' AND episode_number = ${Number(episodeNumber)}"\nReturned no results.`
  );
}

// Get the hidden property from anime_metadata table
let { data: metadataQuery, error: metadataError } = await supabase
  .from('anime_metadata')
  .select('hidden')
  .eq('anilist_id', animeId)
  .single();

// Extract relevant information from the query results
const episodeID = animeQuery[0].id;
const thumbnailUrl = animeQuery[0].thumbnail_link;
const episodeEmoji = numberToDiscordEmoji(episodeNumber);
const animeTitle = animeQuery[0].anime_title.replaceAll('_', ' ');
const isHidden = metadataQuery?.hidden || false; // Get hidden property from metadata and default to false if not present

// // Determine which Discord role to ping for this anime
let rolePing = idToRolePing(animeId);
if (!rolePing) {
  rolePing = null;
}

// Prepare the title for the Docchi system (an external anime tracking platform)
let docchiTitle = titleToDocchiTitle(animeTitle);
if (docchiTitle) {
  await createDocchiEpisode(docchiTitle, episodeNumber, thumbnailUrl, animeTitle, animeId);
} else {
  await createDocchiEpisode(animeTitle, episodeNumber, thumbnailUrl, null, animeId);
}

// Publish the episode information to various Discord channels
await publishToDiscord(animeId, animeTitle, episodeEmoji, rolePing, thumbnailUrl, episodeNumber, publicWebhookUrl, null, isHidden);
await publishToDev(animeId, animeTitle, episodeNumber, devWebhookUrl);

// Post the episode information in the appropriate forum thread
const forumPostId = getForumPostId(animeId.replaceAll(' ', '_'));
await publishToDiscord(animeId, animeTitle, episodeEmoji, rolePing, thumbnailUrl, episodeNumber, forumWebhookUrl, forumPostId, isHidden);


/**
 * Publishes anime episode information to a Discord channel.
 * @async
 * @param {string} animeId - The title of the anime.
 * @param {string} episodeEmoji - The emoji representation of the episode number.
 * @param {string|null} rolePing - The role to ping, if any.
 * @param {string} thumbnailUrl - The URL of the episode thumbnail.
 * @param {number} episodeNumber - The episode number.
 * @param {string} webhookUrl - The Discord webhook URL.
 * @param {string|null} [forumPostId=null] - The ID of the forum post, if applicable.
 * @param {boolean} [isHidden=false] - Whether the anime should display a direct link or search prompt.
 */
async function publishToDiscord(
  animeId,
  animeTitle,
  episodeEmoji,
  rolePing,
  thumbnailUrl,
  episodeNumber,
  webhookUrl,
  forumPostId = null,
  isHidden = false
) {
  if (isHidden) return;
  const webhookClient = new WebhookClient({ url: webhookUrl });

  let animeTitleLink = animeTitle.toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

  // Determine description based on hidden status
  const watchLinkText = isHidden
    ? "Wyszukaj na AnimeZone"
    : `[[KLIKNIJ TUTAJ!]](https://www.lycoris.cafe/anime/${animeId}/${animeTitleLink}/watch/${Number(episodeNumber)})`;

  const embed = new EmbedBuilder()
    .setTitle(`${episodeEmoji} ${animeTitle.replaceAll('_', ' ')}`)
    .setDescription(
      `**Obejrzyj online: ${watchLinkText} \nPobierz odcinek: [PRZYCISKI PONIŻEJ]**`
    )
    .setImage(thumbnailUrl);

  // Create buttons using animeId instead of title
  const button1 = new ButtonBuilder()
    .setLabel('MKV')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=mkv`)
    .setStyle(ButtonStyle.Link);
  const button2 = new ButtonBuilder()
    .setLabel('1080p')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=1080p`)
    .setStyle(ButtonStyle.Link);
  const button3 = new ButtonBuilder()
    .setLabel('720p')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=720p`)
    .setStyle(ButtonStyle.Link);
  const button4 = new ButtonBuilder()
    .setLabel('480p')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=480p`)
    .setStyle(ButtonStyle.Link);
  const button5 = new ButtonBuilder()
    .setLabel('Napisy PL')
    .setURL(`https://www.lycoris.cafe/download?id=${animeId}&episode=${episodeNumber}&q=pl`)
    .setStyle(ButtonStyle.Link);
  const row = new ActionRowBuilder().addComponents(button1, button2, button3, button4, button5);

  // Send the message to the appropriate channel or forum thread
  if (forumPostId) {
    // Send to the specific thread in the forum channel
    webhookClient.send({
      embeds: [embed],
      components: [row],
      allowed_mentions: {
        parse: ['everyone', 'users'],
      },
      threadId: forumPostId,
    });
  } else {
    // Send to the general channel
    webhookClient.send({
      content: rolePing,
      embeds: [embed],
      components: [row],
      allowed_mentions: {
        parse: ['everyone', 'users'],
      },
    });
  }
}

/**
 * Converts a number to its Discord emoji representation.
 * @param {number} num - The number to convert.
 * @returns {string} The Discord emoji representation of the number.
 */
function numberToDiscordEmoji(num) {
  // Map of numbers to their corresponding Discord emoji codes
  const emojiMap = {
    0: ':zero: ',
    1: ':one: ',
    2: ':two: ',
    3: ':three: ',
    4: ':four: ',
    5: ':five: ',
    6: ':six: ',
    7: ':seven: ',
    8: ':eight: ',
    9: ':nine: ',
  };

  // Convert each digit of the number to its emoji representation
  return String(num)
    .split('')
    .map((digit) => emojiMap[digit])
    .join('');
}

/**
 * Maps an anime title to its corresponding Discord role ping.
 * @param {string} title - The anime title.
 * @returns {string|undefined} The Discord role ping for the anime, or undefined if not found.
 */

function idToRolePing(id) {
  return rolePings[id];
}

/**
 * Gets the forum post ID for a given anime title.
 * @param {string} title - The anime title.
 * @returns {string|undefined} The forum post ID, or undefined if not found.
 */

function getForumPostId(animeId) {
  return forumPosts[animeId];
}

/**
 * Delays execution for a specified number of milliseconds.
 * @param {number} ms - The number of milliseconds to sleep.
 * @returns {Promise<void>} A promise that resolves after the specified delay.
 */
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Creates a new episode entry in the Docchi website.
 * @async
 * @param {string} docchiTitle - The title of the anime in Docchi's website (typically taken from MyAnimeList).
 * @param {number} episodeNumber - The episode number.
 * @param {string} thumbnailLink - The URL of the episode thumbnail.
 * @param {string|null} animeTitle - The original anime title, if different from docchiTitle.
 */
async function createDocchiEpisode(docchiTitle, episodeNumber, thumbnailLink, animeTitle) {
  console.log(docchiTitle, episodeNumber, animeTitle);
  const docchiUrl = 'https://worker.docchi.pl/partner/aired';
  const docchiHeaders = {
    'Content-Type': 'application/json',
    authentication: docchiAuthKey,
    clientid: docchiClientID,
  };

  // If no specific animeTitle is provided, use the Docchi title
  if (!animeTitle) {
    animeTitle = docchiTitle;
  }

  // Prepare the data for the Docchi API
  const docchiBody = {
    title: docchiTitle,
    episode: episodeNumber,
    players: [
      {
        url: `https://www.lycoris.cafe/embed?id=${animeId}&episode=${episodeNumber}`,
        player: 'lycoris.cafe',
      },
    ],
    thumbnail: thumbnailLink,
    translator: 'lycoris.cafe',
    translator_media: 'https://lycoris.cafe/discord',
  };

  // Send the request to the Docchi API
  try {
    const response = await axios.post(docchiUrl, docchiBody, { headers: docchiHeaders });

    if (response.status !== 202) {
      await webhookLogs.send({
        content: `Błąd w dodawaniu odcinka :(\n${JSON.stringify(response.data)} <@351006685587963916> napraw`,
      });
      throw new Error(`Błąd w dodawaniu odcinka :(\n${JSON.stringify(response.data)}`);
    }

    console.log(`Odcinek ${animeTitle} - ${episodeNumber} został pomyślnie dodany!`);
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error);
  }
}

/**
 * Maps an anime title to its corresponding Docchi system title.
 * @param {string} title - The original anime title.
 * @returns {string|undefined} The Docchi system title, or undefined if not found.
 */
function titleToDocchiTitle(title) {
  // Use the title directly to access the associated title from the docchiTitleFixes
  return docchiTitleFixes[title];
}

/**
 * Publishes development-related information to a Discord channel.
 * @async
 * @param {string} animeTitle - The title of the anime.
 * @param {number} episodeNumber - The episode number.
 * @param {string} webhookUrl - The Discord webhook URL for the development channel.
 */
async function publishToDev(animeId, animeTitle, episodeNumber, webhookUrl) {
  const data = `\`\`\`\n<iframe src="https://www.lycoris.cafe/embed?title=${animeTitle.replaceAll(' ', '_')}&episode=${episodeNumber}" width="620" height="348" style="border:none;" scrolling="no" allowfullscreen></iframe>\n\`\`\``;
  const webhookClient = new WebhookClient({ url: webhookUrl });
  webhookClient.send({
    content: data,
  });
}