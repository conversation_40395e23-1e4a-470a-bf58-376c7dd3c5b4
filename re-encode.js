import dotenv from 'dotenv';
dotenv.config();
import * as cp from 'child_process';
import fs from 'fs/promises';
import { createClient } from '@supabase/supabase-js';
import axios from 'axios';

const supabaseUrl = process.env.SUPABASE_PROJECT_URL;
const supabaseKey = process.env.SUPABASE_ADMIN_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Fetch user token for video link retrieval
 * @param {string} deviceID 
 * @returns {Promise<string>}
 */
async function fetchUserToken(deviceID) {
  const data = {
    login: '<EMAIL>',
    password: 'mMPtNj30c^1K$Ef6$yGd',
    device_id: deviceID
  };

  const config = {
    method: 'put',
    maxBodyLength: Infinity,
    url: 'https://apis.uloz.to/v6/session',
    headers: {
      'X-Auth-Token': ';HG%7jW6@6/8vx">R;f(',
      'Content-Type': 'application/json'
    },
    data: JSON.stringify(data)
  };

  const response = await axios.request(config);
  return response.data.token_id;
}

/**
 * Fetch video link from slug
 * @param {string} videoSlug 
 * @param {string} deviceID 
 * @param {string} userToken 
 * @returns {Promise<string>}
 */
async function fetchVideoLink(videoSlug, deviceID, userToken) {
  const data = {
    file_slug: videoSlug,
    device_id: deviceID,
    user_login: '<EMAIL>'
  };

  const config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: 'https://apis.uloz.to/v5/file/download-link/vipdata',
    headers: {
      'X-User-Token': userToken,
      'X-Auth-Token': ';HG%7jW6@6/8vx">R;f(',
      'Content-Type': 'application/json'
    },
    data: JSON.stringify(data)
  };

  const response = await axios.request(config);
  return response.data.link;
}

/**
 * Process video links and fetch actual URLs if needed
 * @param {Object} videoLinks 
 * @param {boolean} isYandex 
 * @returns {Promise<Object>}
 */
async function resolveVideoLinks(videoLinks) {
  for (const key in videoLinks) {
    if (videoLinks[key] && typeof videoLinks[key] === 'string' && videoLinks[key].includes('pixeldrain')) {
      return null;
    }
  }

  const deviceID = '4Lajf-PC';
  const userToken = await fetchUserToken(deviceID);
  const resolvedLinks = {};

  for (const [quality, slug] of Object.entries(videoLinks)) {
    if (!slug) continue;
    try {
      resolvedLinks[quality] = await fetchVideoLink(slug, deviceID, userToken);
      console.log(`Resolved link for quality ${quality}`);
      // Add small delay between requests
      await sleep(500);
    } catch (error) {
      console.error(`Failed to resolve link for quality ${quality}:`, error.message);
    }
  }

  return resolvedLinks;
}

/**
 * Process video links into upload configuration
 * @param {Object} videoLinks 
 * @param {string} animeTitle 
 * @param {string} episodeNumber 
 * @returns {Object}
 */
function processVideoLinks(videoLinks, animeTitle, episodeNumber) {
  const qualityMap = {
    'FHD': '1080p',
    'HD': '720p',
    'SD': '480p',
    'Source': 'source',
    'SourceMKV': 'source-mkv'
  };

  const fileUrls = {};

  for (const [quality, fileId] of Object.entries(videoLinks)) {
    if (!fileId) continue;
    fileUrls[qualityMap[quality]] = fileId;
  }

  return {
    title: animeTitle,
    episodeNumber,
    fileUrls
  };
}

function executeCommand(command, args, options) {
  return new Promise((resolve, reject) => {
    const childProcess = cp.spawn(command, args, options);

    childProcess.on('error', (err) => {
      reject(err);
    });

    childProcess.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`Command failed with exit code ${code}`));
      } else {
        resolve();
      }
    });
  });
}

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// Main execution
async function main() {
  try {
    let { data: animeQuery, error } = await supabase
      .from('anime')
      .select('id, anime_title, episode_number, primary_source, secondary_source, subtitleLinks, is_encrypted');

    if (error) {
      console.error('Error fetching from Supabase:', error);
      process.exit(1);
    }

    animeQuery = animeQuery.sort((a, b) => b.id - a.id);
    let counter = parseInt(await fs.readFile('counter.txt', 'utf8'));

    for (let i = counter; i > 0; i--) {
      await fs.writeFile('counter.txt', i.toString());

      const anime = animeQuery.find(anime => anime.id === i);
      if (!anime) continue;

      const animeTitle = anime.anime_title
      const episodeNumber = anime.episode_number.toString().padStart(2, '0');
      const fileName = `[lycoris.cafe] ${animeTitle} - ${episodeNumber}.ass`;

      // Process video links and prepare upload configuration
      if (anime.secondary_source && Object.keys(anime.secondary_source).length > 0) {
        try {
          // Resolve video links if needed
          const resolvedLinks = await resolveVideoLinks(anime.secondary_source);
          if (!resolvedLinks) {
            continue;
          }
          const uploadConfig = processVideoLinks(resolvedLinks, animeTitle, episodeNumber);

          // Write configuration to temporary file for upload script
          const configPath = '.re-encode/5upload/config.json';
          await fs.writeFile(configPath, JSON.stringify(uploadConfig, null, 2));

          // Execute upload script with configuration
          await executeCommand('node', ['.re-encode/5upload/upload.js'], { stdio: 'inherit' });
          console.log(`Successfully processed ${animeTitle} - ${episodeNumber}`);
        } catch (error) {
          console.error(`Failed to process ${animeTitle} - ${episodeNumber}:`, error);
        }
      }

      await sleep(1000);
    }
  } catch (error) {
    console.error('Processing failed:', error);
    process.exit(1);
  }
}

main().catch(console.error);