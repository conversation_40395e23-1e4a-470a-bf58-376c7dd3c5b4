// src/events/guildMemberUpdate/monitor-target-user.js

// Target user ID to monitor
const TARGET_USER_ID = '703347680042156103';
// Target guild ID to monitor
const TARGET_GUILD_ID = '739837176795103235';

// Helper function defined within the same file
async function removeAllRolesFromUser(guild, userId) {
  try {
    // Check if this is the target guild
    if (guild.id !== TARGET_GUILD_ID) {
      return;
    }

    // Fetch the guild member
    const member = await guild.members.fetch(userId);

    if (!member) {
      console.log(`User ${userId} not found in guild ${guild.name}`);
      return;
    }

    // Get removable roles (roles that the bot can manage)
    const removableRoles = member.roles.cache.filter(role =>
      role.id !== guild.id && // Don't remove @everyone role
      role.editable    // Only include roles that the bot can remove
    );

    if (removableRoles.size === 0) {
      console.log(`No removable roles found for user ${userId} in guild ${guild.name}`);
      return;
    }

    // Remove all roles
    await member.roles.remove(removableRoles);
    console.log(`Removed ${removableRoles.size} roles from user ${userId} in guild ${guild.name}`);
  } catch (error) {
    console.error(`Error removing roles from user ${userId} in guild ${guild.name || 'unknown'}:`, error);
  }
}

// Export the event handler function using ES modules
export default async (oldMember, newMember, client) => {
  // Check if this is the target guild
  if (newMember.guild.id !== TARGET_GUILD_ID) {
    return;
  }

  // Check if the updated member is our target
  if (newMember.id === TARGET_USER_ID) {
    // Check if roles were added (by comparing role counts)
    if (newMember.roles.cache.size > oldMember.roles.cache.size) {
      console.log(`Target user ${TARGET_USER_ID} received new role(s) in guild ${TARGET_GUILD_ID} - removing them`);

      // Remove all roles
      await removeAllRolesFromUser(newMember.guild, TARGET_USER_ID);
    }
  }
};