/**
 * @file Cleanup Utility Script
 * @description This script performs cleanup of temporary files and directories.
 * It can be called independently or from other scripts to ensure proper cleanup
 * even in case of errors.
 */

import fs from 'fs';
import path from 'path';

// Define color codes for console output
const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  CYAN: '\x1b[36m',
  GRAY: '\x1b[90m',
};

// List of directories to clean
const DIRECTORIES_TO_CLEAN = [
  '5upload/final',
  '5upload/subPL',
  '5upload/subEN',
  '5upload/thumbInfo',
  '5upload/thumbTemp',
  '4encode',
  '0download',
];

/**
 * Deletes all files in a directory except .gitkeep and .js files
 * @param {string} directoryPath - The path to the directory to clean
 * @returns {Promise<void>}
 */
async function deleteAllFilesInDirectory(directoryPath) {
  try {
    const files = fs.readdirSync(directoryPath).filter((file) => file !== '.gitkeep' && !file.endsWith('.js'));
    for (const file of files) {
      const filePath = path.join(directoryPath, file);
      try {
        fs.unlinkSync(filePath);
        console.log(`${COLORS.GRAY}[INFO] Deleted file: ${filePath}${COLORS.RESET}`);
      } catch (error) {
        console.error(`${COLORS.RED}[ERROR] Error deleting file ${filePath}: ${error.message}${COLORS.RESET}`);
      }
    }
    console.info(`${COLORS.GRAY}[INFO] Completed deletion process for directory: ${directoryPath}${COLORS.RESET}`);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error processing directory: ${directoryPath}${COLORS.RESET}`);
    console.error(error);
  }
}

/**
 * Performs cleanup of all specified directories
 * @param {string[]} dirsToClean - Array of directory paths to clean
 * @returns {Promise<void>}
 */
async function performDirectoryCleanup(dirsToClean) {
  console.info(`${COLORS.CYAN}[INFO] Starting directory cleanup...${COLORS.RESET}`);
  for (const dir of dirsToClean) {
    await deleteAllFilesInDirectory(dir);
  }
  console.info(`${COLORS.GREEN}[INFO] Directory cleanup completed successfully${COLORS.RESET}`);
}

// Execute cleanup when script is run directly
(async function() {
  try {
    await performDirectoryCleanup(DIRECTORIES_TO_CLEAN);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Cleanup process failed: ${error.message}${COLORS.RESET}`);
    process.exit(1);
  }
})();

// Export functions for use in other modules
export { performDirectoryCleanup, deleteAllFilesInDirectory };
