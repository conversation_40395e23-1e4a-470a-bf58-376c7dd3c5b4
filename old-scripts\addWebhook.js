import dotenv from 'dotenv';
dotenv.config();

import { SlashCommandBuilder } from '@discordjs/builders';
import { createClient } from '@supabase/supabase-js';
import { WebhookClient } from 'discord.js';

const supabase = createClient(process.env.SUPABASE_PROJECT_URL, process.env.SUPABASE_ADMIN_KEY);

export const data = new SlashCommandBuilder().setName('test').setDescription('🦌');

export async function run({ interaction, client, handler }) {
  const GUILD_ID = '1368585282995355648';
  const CHANNEL_ID = '1368731575323463880';

  try {
    const guild = await client.guilds.fetch(GUILD_ID);
    const channel = await guild.channels.fetch(CHANNEL_ID);

    const webhook = await channel.createWebhook({
      name: 'lycoris.cafe',
      avatar: 'https://pixeldrain.com/api/file/JATDAeTm',
    });

    console.log(`Webhook created: ${webhook.url}`);
  } catch (error) {
    console.error('Error creating webhook:', error);
  }
}

export const options = {
  devOnly: true,
  deleted: false,
};
