// @ts-check

/**
 * @file Anime Episode Post-Processing and Publishing Script
 * @description This script performs the final steps in processing and publishing anime episodes. It handles the following tasks:
 *
 * 1. File Management:
 *    - Reads video files from a specified directory.
 *    - Groups files by episode and anime name.
 *    - Handles multiple quality versions of the same episode (e.g., 1080p, 720p, 480p).
 *
 * 2. Thumbnail Generation:
 *    - Creates episode thumbnails either from a provided image URL or from the video file.
 *    - Generates and uploads on-seekbar thumbnails for video player use.
 *    - Creates WebVTT files for thumbnail navigation in video players.
 *
 * 3. File Uploads:
 *    - Uploads video files to Uloz.to file hosting service.
 *    - Uploads thumbnails and subtitle files to Pixeldrain.
 *    - Handles FTP uploads for on-seekbar thumbnails.
 *
 * 4. Subtitle Processing:
 *    - Extracts and processes subtitle files (both English and Polish).
 *    - Optionally adds karaoke subtitles for opening and ending themes. (coming soon)
 *
 * 5. Database Operations:
 *    - Creates or updates episode entries in a Supabase database.
 *    - Stores information about video links, subtitles, and thumbnails.
 *
 * 6. OP/ED Marking:
 *    - Attempts to mark the start and end times of opening and ending themes.
 *    - Uses MKV metadata or subtitle timing for estimation.
 *
 * 7. Discord Integration:
 *    - Sends notifications about new episodes to Discord channels.
 *    - Provides embed information for Discord messages.
 *
 * 8. Error Handling and Logging:
 *    - Implements comprehensive error handling throughout the process.
 *    - Logs information and errors to both console and log files.
 *    - Uses color-coded console outputs for better readability.
 *
 * 9. Cleanup Operations:
 *    - Performs cleanup of temporary files and directories after processing.
 *
 * 10. Retry Mechanisms:
 *     - Implements retry logic for various operations like uploads and API calls.
 *
 * 11. File Sharing:
 *     - Enables sharing of uploaded files on file hosting services.
 *
 * 12. Image Processing:
 *     - Converts and optimizes images for web use (e.g., converting to WebP format).
 *
 * 13. Video Information Extraction:
 *     - Extracts metadata from video files (e.g., duration, chapter information).
 *
 * 14. Conditional Publishing:
 *     - Allows for conditional publishing of episodes based on command-line arguments.
 *
 * 15. Multi-platform Integration:
 *     - Integrates with multiple services (Uloz.to, Pixeldrain, Discord, Supabase) for comprehensive episode management.
 *
 * This script serves as the final step in an anime processing pipeline, preparing and
 * publishing episodes for viewer consumption across multiple platforms and quality levels.
 */

//TODO: add upload to opendrive and backup for pixeldrain
import titleMapping from '../_resources/title_mappings.json' assert { type: 'json' };;
import { config } from 'dotenv';
config();
import { createClient } from '@supabase/supabase-js';
import { WebhookClient } from 'discord.js';
import ftp from 'basic-ftp';
import process from 'process';
import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';
import axios from 'axios';
import ffmpeg from 'fluent-ffmpeg';
import * as cp from 'child_process';
import { promisify } from 'util';

import sharp from 'sharp';

const boolPublish = process.argv[2];
const subtitleLinkDev = process.argv[3];
const imageLink = process.argv[4];

const qualityOrder = ['1080p', '720p', '480p', 'source-mkv'];
// @ts-ignore
const webhookFiles = new WebhookClient({ url: process.env.DISCORD_WEBHOOK_FILES });
// @ts-ignore
const supabase = createClient(process.env.SUPABASE_PROJECT_URL, process.env.SUPABASE_ADMIN_KEY);
const execAsync = promisify(cp.exec);
const pixeldrainAPIKey = process.env.PIXELDRAIN_API_KEY;
const pixeldrainUsername = process.env.PIXELDRAIN_USERNAME;
const pixeldrainAPIKeyFilesystem = process.env.PIXELDRAIN_API_KEY_FILESYSTEM;
const baseDirectory = '5upload/final';
const animeToCorrect = [];
let isDuplicate;
// @ts-ignore
const webhookClient = new WebhookClient({ url: process.env.DISCORD_WEBHOOK_LOGS });

/**
 * @constant {Object} COLORS - ANSI color codes for console output formatting.
 */
const COLORS = {
  RESET: '\x1b[0m',
  BLACK: '\x1b[30m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
  DIM: '\x1b[2m',
  BG_BLACK: '\x1b[40m',
  BG_RED: '\x1b[41m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
  BG_BLUE: '\x1b[44m',
  BG_MAGENTA: '\x1b[45m',
  BG_CYAN: '\x1b[46m',
  BG_WHITE: '\x1b[47m',
};

(async function () {
  try {
    const directoryPath = baseDirectory;
    const files = await fs.promises.readdir(directoryPath);
    const videoFiles = files.filter((file) => {
      const ext = path.extname(file);
      return ext === '.mp4' || ext === '.mkv';
    });

    if (videoFiles.length === 0) {
      console.info(`${COLORS.GRAY}[INFO] No video files found in the final upload directory.${COLORS.RESET}`);
      return;
    }

    // Group video files by episode and anime name
    const episodeGroups = groupVideoFilesByEpisode(videoFiles);

    for (const episodeKey in episodeGroups) {
      const episodeFiles = episodeGroups[episodeKey].sort((a, b) => {
        const getQualityIndex = (filename) => {
          for (let i = 0; i < qualityOrder.length; i++) {
            if (filename.includes(qualityOrder[i])) {
              return i;
            }
          }
          return qualityOrder.length;
        };

        return getQualityIndex(a) - getQualityIndex(b);
      });

      const title = extractTitleFromFileName(episodeFiles[0]);
      const episodeNumber = extractEpisodeNumberFromFileName(episodeFiles[0]);
      const animeId = getAnilistId(title);

      if (!animeId) {
        console.error(`${COLORS.RED}[ERROR] No AniList ID found for ${title}${COLORS.RESET}`);
        continue;
      }

      const metadata = await getAnimeMetadata(animeId);
      if (!metadata) {
        console.error(`${COLORS.RED}[ERROR] No metadata found for AniList ID ${animeId}${COLORS.RESET}`);
        continue;
      }

      // @ts-ignore
      isDuplicate = await isEpisodeAlreadyCreated(animeId, Number(episodeNumber));
      if (!isDuplicate) {
        await generateAndUploadThumbnails(`5upload/final/${episodeFiles[0]}`, title, episodeNumber);
      }

      // Upload all video files concurrently
      // @ts-ignore
      const pixeldrainResults = await uploadVideos(episodeFiles, directoryPath, pixeldrainAPIKey);

      const formattedResults = {
        FHD: pixeldrainResults['1080p'],
        HD: pixeldrainResults['720p'],
        SD: pixeldrainResults['480p'],
        SourceMKV: pixeldrainResults['source-mkv'],
        preview: pixeldrainResults['preview']
      };

      await handleEpisodeProcessing(
        title,
        episodeNumber,
        {
          "HD": "",
          "SD": "",
          "FHD": "",
          "preview": "",
          "SourceMKV": ""
        },
        formattedResults,
        animeId
      );

      await sleep(2500);

      embedInfoToDiscord(`https://www.lycoris.cafe/embed?id=${animeId}&episode=${episodeNumber}`);

      if (boolPublish == 'true') {
        cp.spawnSync('node', ['publishEpisode.js', animeId, episodeNumber], {
          stdio: 'inherit',
        });
      }
    }

    // Use the cleanup utility script for consistency
    await executeCommand('node', ['cleanup.js'], { stdio: 'inherit' });

    await updateMALIds();

  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Upload process failed: ${error.message}${COLORS.RESET}`);

    // Clean up directories even in case of errors
    try {
      console.info(`${COLORS.YELLOW}[INFO] Performing cleanup after error...${COLORS.RESET}`);

      // Use the cleanup utility script
      await executeCommand('node', ['cleanup.js'], { stdio: 'inherit' });

      console.info(`${COLORS.GREEN}[INFO] Cleanup after error completed successfully${COLORS.RESET}`);
    } catch (cleanupError) {
      console.error(`${COLORS.RED}[ERROR] Failed to clean up after error: ${cleanupError.message}${COLORS.RESET}`);
    }

    process.exit(1);
  }
})();

async function getAnimeMetadata(animeId) {
  const { data: metadata, error } = await supabase
    .from('anime_metadata')
    .select('romaji_title')
    .eq('anilist_id', animeId)
    .single();

  if (error) {
    console.error(`${COLORS.RED}[ERROR] Failed to fetch anime metadata: ${error.message}${COLORS.RESET}`);
    return null;
  }

  return metadata;
}

async function getAnimeEpisodes(malId) {
  try {
    const allEpisodes = [];
    let hasNextPage = true;
    let currentPage = 1;

    while (hasNextPage) {
      const response = await axios.get(
        `https://api.jikan.moe/v4/anime/${malId}/episodes?page=${currentPage}`
      );

      allEpisodes.push(...response.data.data);
      hasNextPage = response.data.pagination.has_next_page;
      currentPage++;

      await sleep(1500); // Jikan rate limit
    }

    return allEpisodes;
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error fetching episodes for MAL ID ${malId}: ${error.message}${COLORS.RESET}`);
    return null;
  }
}

async function getAniListEpisodes(anilistId) {
  try {
    const response = await fetch('https://graphql.anilist.co', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query ($id: Int) {
            Media(id: $id) {
              episodes
              streamingEpisodes {
                title
                site
                url
              }
            }
          }
        `,
        variables: { id: anilistId }
      })
    });

    const data = await response.json();
    // @ts-ignore
    if (data.errors) return null;

    // @ts-ignore
    return data.data.Media.streamingEpisodes;
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error fetching AniList episodes for ID ${anilistId}: ${error.message}${COLORS.RESET}`);
    return null;
  }
}

async function updateEpisodeTitle(malId, anilistId, episodeNumber) {
  let episodeTitle = null;

  // Try Jikan API first
  if (malId) {
    const episodes = await getAnimeEpisodes(malId);
    if (episodes) {
      const episode = episodes.find(ep => ep.mal_id === episodeNumber);
      if (episode) {
        episodeTitle = episode.title.replace(/^Episode \d+[:\s-]*\s*/i, '').trim();
      }
    }
  }

  // If no title found, try AniList API
  if (!episodeTitle && anilistId) {
    await sleep(2500); // AniList rate limit
    const episodes = await getAniListEpisodes(anilistId);
    if (episodes) {
      const episode = episodes.find(ep => {
        const titleMatch = ep.title.match(/Episode\s*(\d+)/i);
        const urlMatch = ep.url.match(/episode-(\d+)/i);
        const epNum = titleMatch ? parseInt(titleMatch[1]) : urlMatch ? parseInt(urlMatch[1]) : null;
        return epNum === episodeNumber;
      });

      if (episode) {
        episodeTitle = episode.title
          .replace(/^Episode \d+[:\s-]*\s*/i, '')
          .replace(/^[-\s]*/, '')
          .trim();
      }
    }
  }

  return episodeTitle;
}

/**
 * Stop executing the script for a given amount of miliseconds
 * @param {number} ms - The number of milliseconds to sleep
 * @returns {Promise} A promise that resolves after the specified number of milliseconds
 */
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Uploads multiple video files to Pixeldrain concurrently
 * @param {Array<string>} files - Array of video file paths
 * @param {string} directoryPath - Base directory path
 * @param {string} apiKey - Pixeldrain API key
 * @returns {Promise<Object>} Object mapping quality levels to Pixeldrain URLs
 */
async function uploadVideos(files, directoryPath, apiKey) {
  const results = {};

  try {
    // First upload all the regular quality versions
    const regularUploadPromises = files.filter(file => !file.includes('preview')).map(file => {
      const resolution = qualityOrder.find(res => file.includes(res));
      const filePath = path.join(directoryPath, file);

      return uploadFileToPixeldrain(filePath, apiKey)
        .then(id => {
          results[resolution] = `https://pixeldrain.com/api/file/${id}`;
        });
    });

    // Then upload preview file if it exists
    const previewFile = files.find(file => file.includes('preview'));
    if (previewFile) {
      const previewPath = path.join(directoryPath, previewFile);
      const previewUploadPromise = uploadFileToPixeldrain(previewPath, apiKey)
        .then(id => {
          results['preview'] = `https://pixeldrain.com/api/file/${id}`;
        });

      regularUploadPromises.push(previewUploadPromise);
    }

    await Promise.all(regularUploadPromises);
    return results;
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Video upload failed: ${error.message}${COLORS.RESET}`);
    throw error;
  }
}

/**
 * Group the same episodes (same title, episode number like "XYZ [1080p], XYZ [720p]")
 * @param {Array} videoFiles - An array of video file names
 * @returns {Object} An object containing grouped video files by title and episode
 */
function groupVideoFilesByEpisode(videoFiles) {
  const episodeGroups = {};
  for (const file of videoFiles) {
    const episodeKey = extractTitleFromFileName(file);
    const episodeNumber = extractEpisodeNumberFromFileName(file);
    const groupKey = `${episodeKey} ${episodeNumber}`;

    if (!episodeGroups[groupKey]) {
      episodeGroups[groupKey] = [];
    }

    episodeGroups[groupKey].push(file);
  }

  return episodeGroups;
}

/**
 * @param {string} filename - The name of the file
 * @returns {string} The extracted title from the file name, or null if no match is found
 */
function extractTitleFromFileName(filename) {
  // Check if this is a ToonsHub file (case-insensitive)
  const isToonsHub = filename.toLowerCase().includes('toonshub') ||
                     filename.match(/\.s\d+e\d+\./i);

  if (isToonsHub) {
    // For ToonsHub files, extract title differently
    const toonsHubInfo = extractToonsHubInfo(filename);
    if (toonsHubInfo) {
      return toonsHubInfo.title;
    }
    throw new Error('Failed to extract title from ToonsHub filename');
  } else {
    // For Erai-raws files, use the existing logic
    const processedFilename = filename.replace('Erai-raws_', '')
      .replace('lycoris.cafe_', '')
      .replace('[lycoris.cafe] ', '')
      .replaceAll('_', ' ');

    const match = processedFilename.match(/^(.*)\s-\s[^\n]*$/);
    if (!match || !match[1]) {
      throw new Error('Failed to extract title from filename');
    }
    return match[1];
  }
}

/**
 * @param {string} fileName - The name of the file
 * @returns {string} The extracted episode number from the file name
 */
function extractEpisodeNumberFromFileName(fileName) {
  // Check if this is a ToonsHub file (case-insensitive)
  const isToonsHub = fileName.toLowerCase().includes('toonshub') ||
                     fileName.match(/\.s\d+e\d+\./i);

  if (isToonsHub) {
    // For ToonsHub files, extract episode number differently
    const toonsHubInfo = extractToonsHubInfo(fileName);
    if (toonsHubInfo) {
      return toonsHubInfo.episode;
    }
    throw new Error('Failed to extract episode number from ToonsHub filename');
  } else {
    // For Erai-raws files, use the existing logic
    const processedFilename = fileName.replace('Erai-raws_', '')
      .replace('lycoris.cafe_', '')
      .replace('[lycoris.cafe] ', '')
      .replaceAll('_', ' ');

    const match = processedFilename.match(/(?<=-\s*)\d+/);
    if (!match || !match[0]) {
      throw new Error('Failed to extract episode number from filename');
    }
    return match[0];
  }
}


async function handleEpisodeProcessing(
  title,
  episodeNumber,
  primarySource,
  secondarySource,
  animeId
) {
  const metadata = await getAnimeMetadata(animeId);
  if (!metadata) {
    throw new Error(`No metadata found for AniList ID ${animeId}`);
  }
  const animeTitle = title

  const fullTitle = `${animeTitle} - ${episodeNumber}`;
  const sourceFilePath = findFile(fullTitle, '0download');
  const markerPeriods = await markOPED(
    `5upload/subPL/[lycoris.cafe] ${animeTitle} - ${episodeNumber}.ass`,
    `0download/${sourceFilePath}`
  );

  let thumbnailPath;
  if (imageLink) {
    thumbnailPath = await downloadAndProcessImage(imageLink, animeTitle, episodeNumber);
  } else {
    thumbnailPath = await createThumbnail(`0download/${sourceFilePath}`, sourceFilePath.replace('.mp4', ''));
  }
  const thumbnailUploadData = await uploadFileToPixeldrain(thumbnailPath, pixeldrainAPIKey);

  const PLsubtitleFile = `5upload/subPL/[lycoris.cafe] ${animeTitle} - ${episodeNumber}.ass`;
  const polishSubsOnly = await uploadFileToPixeldrain(PLsubtitleFile, pixeldrainAPIKey);

  const ENsubtitleFile = `5upload/subEN/[lycoris.cafe] ${animeTitle} - ${episodeNumber} [EN].ass`;
  const englishSubsOnly = await uploadFileToPixeldrain(ENsubtitleFile, pixeldrainAPIKey);

  const previewFile = `[lycoris.cafe] ${animeTitle} - ${episodeNumber} [preview].mp4`;
  const previewPath = `5upload/final/${previewFile}`;
  const previewUploadId = await uploadFileToPixeldrain(previewPath, pixeldrainAPIKey);
  const previewLink = `https://pixeldrain.com/api/file/${previewUploadId}`;

  let thumbnailFile = '';
  if (!isDuplicate) {
    thumbnailFile = fs.readFileSync(`5upload/thumbInfo/${animeTitle} - ${episodeNumber}.txt`, 'utf8').toString();
  }

  if (isDuplicate) {
    await updateEpisode(
      animeId,
      Number(episodeNumber),
      secondarySource,
      {
        PL: `https://pixeldrain.com/api/file/${polishSubsOnly}`,
        EN: `https://pixeldrain.com/api/file/${englishSubsOnly}`,
      },
      previewLink
    );
  } else {
    await createEpisode(
      primarySource,
      secondarySource,
      animeId,
      Number(episodeNumber),
      'lato2023',
      `https://pixeldrain.com/api/file/${thumbnailUploadData}`,
      {
        PL: `https://pixeldrain.com/api/file/${polishSubsOnly}`,
        EN: `https://pixeldrain.com/api/file/${englishSubsOnly}`,
      },
      JSON.stringify(markerPeriods),
      thumbnailFile,
      previewLink
    );
  }

  await webhookClient.send({
    content: `[INFO] Successfully processed ${animeTitle} - ${episodeNumber} with preview clip`,
  });

  console.info(
    `${COLORS.CYAN}[INFO] Successfully processed ${animeTitle} - ${episodeNumber} with preview clip${COLORS.RESET}`
  );
}

/**
 * Downloads an image from a given URL, converts it to WebP, and saves it
 * @param {string} imageUrl - The URL of the image to download
 * @param {string} title - The title of the episode
 * @param {string} episodeNumber - The episode number
 * @returns {Promise<string>} A promise that resolves to the path of the processed image
 */
async function downloadAndProcessImage(imageUrl, title, episodeNumber) {
  try {
    console.info(`${COLORS.CYAN}[INFO] Downloading and processing image...${COLORS.RESET}`);

    const response = await fetch(imageUrl);
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    const fullTitle = `[lycoris.cafe] ${title} - ${episodeNumber}`;
    const outputPath = `5upload/final/${fullTitle}.webp`;

    await sharp(buffer).webp({ quality: 100 }).toFile(outputPath);

    console.info(`${COLORS.CYAN}[INFO] Image processed and saved: ${outputPath}${COLORS.RESET}`);
    return outputPath;
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error processing image: ${COLORS.RESET}`, error);
    throw error;
  }
}

function fixTitle(title) {
  const titleMap = {
    'NieR-Automata Ver1_1a Part 2': 'NieR:Automata Ver1.1a Part 2',
  };

  // Use the title directly to access the associated role from the roleMap
  return titleMap[title];
}

/**
 * @returns {Promise} A promise that resolves when the directory cleanup is complete
 * @param {string[]} dirsToClean
 */
async function performDirectoryCleanup(dirsToClean) {
  for (const dir of dirsToClean) {
    await deleteAllFilesInDirectory(dir);
  }
}

/**
 * Generates and uploads on-seekbar thumbnails in 400x225 resolution for the player to use.
 * @param {string} inputPath - The path to the input video file
 * @param {string} title - The title of the episode
 * @param {string} episodeNumber - The episode number
 * @returns {Promise} A promise that resolves when the thumbnails are generated and uploaded
 */
async function generateAndUploadThumbnails(inputPath, title, episodeNumber) {
  // console.info(`${COLORS.GRAY}[INFO] Generating thumbnails from: ${inputPath}...${COLORS.RESET}`);

  // // Get video duration using ffprobe
  // const duration = await getVideoDuration(inputPath);
  // const interval = 5; // 5 second intervals
  // const numSegments = Math.floor(duration / interval);
  // const thumbnailTasks = [];

  // // Create thumbnail generation tasks
  // for (let i = 0; i < numSegments; i++) {
  //   const timestamp = i * interval;
  //   const outputPath = path.join('5upload/thumbTemp', `output${String(i + 1).padStart(3, '0')}.webp`);
  //   thumbnailTasks.push({
  //     timestamp,
  //     outputPath,
  //     index: i
  //   });
  // }

  // // Generate all thumbnails in parallel
  // console.info(`${COLORS.GRAY}[INFO] Creating ${thumbnailTasks.length} thumbnails in parallel...${COLORS.RESET}`);
  // const thumbnailPromises = thumbnailTasks.map(async task => {
  //   try {
  //     await generateSingleThumbnail(inputPath, task.timestamp, task.outputPath);
  //     // console.info(`${COLORS.GRAY}[INFO] Created thumbnail: ${path.basename(task.outputPath)}${COLORS.RESET}`);
  //     return {
  //       path: task.outputPath,
  //       index: task.index,
  //       success: true
  //     };
  //   } catch (error) {
  //     console.error(`${COLORS.RED}[ERROR] Failed to create thumbnail at ${task.timestamp}s: ${error.message}${COLORS.RESET}`);
  //     return {
  //       path: task.outputPath,
  //       index: task.index,
  //       success: false
  //     };
  //   }
  // });

  // const generatedThumbnails = await Promise.all(thumbnailPromises);
  // const failedThumbnails = generatedThumbnails.filter(t => !t.success);

  // if (failedThumbnails.length > 0) {
  //   throw new Error(`Failed to generate ${failedThumbnails.length} thumbnails`);
  // }

  // // Sort thumbnails by index to maintain order
  // const sortedThumbnails = generatedThumbnails
  //   .sort((a, b) => a.index - b.index)
  //   .map(t => path.basename(t.path));

  // // Upload thumbnails to FTP
  // const fileID = await uploadThumbnailsToFTP(title, episodeNumber);
  // if (!fileID) {
  //   throw new Error('Failed to upload thumbnails');
  // }

  // Generate WebVTT file
  const outputPath = `5upload/thumbTemp/${title} - ${episodeNumber}.txt`;
  const outputPath2 = `5upload/thumbInfo/${title} - ${episodeNumber}.txt`;
  fs.writeFileSync(outputPath, '', 'utf8');
  fs.writeFileSync(outputPath2, '', 'utf8');
  // generateWebVTT(fileID, sortedThumbnails.length, title, episodeNumber, sortedThumbnails);

  console.info(`${COLORS.CYAN}[INFO] Thumbnails processed successfully!${COLORS.RESET}`);
}

async function generateSingleThumbnail(inputPath, timestamp, outputPath) {
  return new Promise((resolve, reject) => {
    ffmpeg(inputPath)
      .screenshots({
        timestamps: [timestamp],
        filename: path.basename(outputPath),
        folder: path.dirname(outputPath),
        size: '400x225'
      })
      // @ts-ignore
      .on('end', () => resolve())
      .on('error', (err) => reject(err));
  });
}

async function getVideoDuration(inputPath) {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(inputPath, (err, metadata) => {
      if (err) {
        reject(err);
        return;
      }
      // @ts-ignore
      resolve(Math.floor(metadata.format.duration));
    });
  });
}

/**
 * Uploads thumbnails to a storage to be later used when creating thumbnail list file
 * @param {string} title - The title of the episode
 * @param {string} episodeNumber - The episode number
 * @returns {Promise} A promise that resolves when the thumbnails are uploaded to `FTP`
 */
async function uploadThumbnailsToFTP(title, episodeNumber) {
  const maxRetries = 3;
  const uploadDelay = 5000;

  // Get and sort thumbnail files
  const files = fs.readdirSync('5upload/thumbTemp')
    .filter(file => file.endsWith('.webp'))
    .sort((a, b) => {
      // @ts-ignore
      const numA = parseInt(a.match(/\d+/)[0]);
      // @ts-ignore
      const numB = parseInt(b.match(/\d+/)[0]);
      return numA - numB;
    });

  // Create upload tasks with index tracking
  const uploadTasks = files.map((file, index) => ({
    file,
    index,
    localPath: path.join('5upload/thumbTemp', file),
    remotePath: `/${title} - ${episodeNumber}/${file}`,
    attempts: 0,
    uploaded: false
  }));

  async function uploadFile(task) {
    const client = new ftp.Client();

    try {
      await client.access({
        host: 'pixeldrain.com',
        port: 990,
        user: pixeldrainUsername,
        password: pixeldrainAPIKeyFilesystem,
        secure: 'implicit',
      });

      await client.ensureDir(`/${title} - ${episodeNumber}`);
      await client.uploadFrom(task.localPath, task.remotePath);

      console.log(`Uploaded: ${task.file} (${task.index + 1}/${files.length})`);
      return { ...task, uploaded: true };
    } catch (error) {
      console.error(`Upload failed for ${task.file}, attempt ${task.attempts + 1}: ${error.message}`);
      return { ...task, uploaded: false, attempts: task.attempts + 1 };
    } finally {
      client.close();
    }
  }

  // Initial parallel upload attempt
  let results = await Promise.all(
    uploadTasks.map(task => uploadFile(task))
  );

  // Retry failed uploads
  while (results.some(r => !r.uploaded && r.attempts < maxRetries)) {
    const failedTasks = results.filter(r => !r.uploaded && r.attempts < maxRetries);
    console.log(`Retrying ${failedTasks.length} failed uploads...`);

    await sleep(uploadDelay);

    const retryResults = await Promise.all(
      failedTasks.map(task => uploadFile(task))
    );

    // Update results with retry outcomes
    retryResults.forEach(retryResult => {
      const index = results.findIndex(r => r.index === retryResult.index);
      if (index !== -1) {
        results[index] = retryResult;
      }
    });
  }

  // Check if all uploads were successful
  const failedUploads = results.filter(r => !r.uploaded);
  if (failedUploads.length > 0) {
    const failedFiles = failedUploads.map(f => f.file).join(', ');
    throw new Error(`Failed to upload files after ${maxRetries} attempts: ${failedFiles}`);
  }

  // Enable sharing and get file ID
  const fileID = await enableSharing(encodeURI(`${title} - ${episodeNumber}`));
  if (!fileID) {
    throw new Error('Failed to enable sharing');
  }

  // Sort results by original index and generate WebVTT
  const sortedFiles = results
    .sort((a, b) => a.index - b.index)
    .map(r => r.file);

  // Generate and upload WebVTT file
  const webVTTContent = generateWebVTT(fileID, sortedFiles.length, title, episodeNumber, sortedFiles);
  fs.writeFileSync(`5upload/thumbTemp/${title} - ${episodeNumber}.txt`, webVTTContent);
  const webVTTPath = `5upload/thumbTemp/${title} - ${episodeNumber}.txt`;

  // Upload WebVTT file to pixeldrain
  const webVTTId = await uploadFileToPixeldrain(webVTTPath, pixeldrainAPIKey);
  const webVTTLink = `https://pixeldrain.com/api/file/${webVTTId}`;

  // Save pixeldrain link to thumbInfo directory
  fs.writeFileSync(`5upload/thumbInfo/${title} - ${episodeNumber}.txt`, webVTTLink);

  await webhookClient.send({
    content: `[INFO] Thumbnails and WebVTT file uploaded successfully!`,
  });

  console.info(`${COLORS.CYAN}[INFO] All thumbnails and WebVTT file uploaded successfully!${COLORS.RESET}`);
  return fileID;
}

/**
 * Enables sharing of the directory to which the thumbnails were uploaded
 * @param {string} dir - The directory to enable sharing for
 * @returns {Promise<string | undefined>} A promise that resolves to the file ID if sharing is enabled, or undefined if it fails
 */
async function enableSharing(dir) {
  const url = `https://pixeldrain.com/api/filesystem/me/${dir}`;
  const headers = {
    accept: '*/*',
    'accept-language': 'pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7',
    'cache-control': 'no-cache',
    'content-type': 'multipart/form-data; boundary=----WebKitFormBoundaryKxJFhAGIGMAW7VAO',
    pragma: 'no-cache',
    'sec-ch-ua': '"Google Chrome";v="123", "Not:A-Brand";v="8", "Chromium";v="123"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    cookie: `pd_auth_key=${process.env.PIXELDRAIN_API_KEY_FILESYSTEM}`,
    Referer: `https://pixeldrain.com/d/me/${dir}`,
    'Referrer-Policy': 'strict-origin-when-cross-origin',
  };

  const body = `------WebKitFormBoundaryKxJFhAGIGMAW7VAO
Content-Disposition: form-data; name="action"

update
------WebKitFormBoundaryKxJFhAGIGMAW7VAO
Content-Disposition: form-data; name="shared"

true
------WebKitFormBoundaryKxJFhAGIGMAW7VAO
Content-Disposition: form-data; name="branding_enabled"


------WebKitFormBoundaryKxJFhAGIGMAW7VAO--`;

  let fileID;
  await fetch(url, {
    method: 'POST',
    headers: headers,
    body: body,
  })
    .then(async (response) => await response.json())
    .then((data) => {
      // @ts-ignore
      fileID = data.id;
    })
    .catch((error) => {
      console.error('Error:', error);
    });
  return fileID;
}

/**
 * Fetches a singular file from the pixeldrain drive using it's index as ID
 * @param {number} index - The index of the upload to fetch (0 being the most recent)
 * @param {string | undefined} apikey
 * @returns {Promise<{id: string, name: string}>} A promise that resolves to the upload data object
 * @throws {Error} If there is an error fetching the data
 */
async function fetchPixeldrainUpload(index, apikey) {
  try {
    const options = {
      method: 'GET',
      headers: {
        Authorization: 'Basic ' + Buffer.from(':' + apikey).toString('base64'),
      },
    };

    const response = await fetch('https://pixeldrain.com/api/user/files', options);
    const jsonData = await response.json();

    // Sort the files based on date_upload in newest first order
    // @ts-ignore
    const sortedFiles = jsonData.files.sort((a, b) => {
      const dateA = new Date(b.date_upload).getTime();
      const dateB = new Date(a.date_upload).getTime();
      return dateA - dateB;
    });

    // Get the "index" upload (newer first)
    const latestFile = sortedFiles[index];
    const id = latestFile.id;
    const name = latestFile.name;
    return {
      id,
      name,
    };
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error fetching data ${COLORS.RESET}`);
    throw new Error(error);
  }
}

/**
 * Generates web VTT file that the player will use to display on-seekbar thumbnails
 * @param {string} fileID - The ID of the file
 * @param {number} numSegments - The number of segments in the WebVTT file
 * @param {string} title - The title of the episode
 * @param {string} episodeNumber - The episode number
 * @returns {string} The generated WebVTT content
 */
function generateWebVTT(fileID, numSegments, title, episodeNumber, thumbnailFiles) {
  const baseURL = `https://pixeldrain.com/api/filesystem/${fileID}/`;
  const duration = 5; // Duration of each segment in seconds
  let webvttContent = 'WEBVTT\n\n';

  for (let i = 0; i < numSegments; i++) {
    const startTime = i * duration;
    const endTime = (i + 1) * duration - 0.001;
    const startTimestamp = formatTimestamp(startTime);
    const endTimestamp = formatTimestamp(endTime);
    const fileURL = `${baseURL}${thumbnailFiles[i]}`;
    webvttContent += `${startTimestamp} --> ${endTimestamp}\n${fileURL}\n\n`;
  }

  const outputPath = `5upload/thumbTemp/${title} - ${episodeNumber}.txt`;
  fs.writeFileSync(outputPath, webvttContent, 'utf8');
  console.info(`${COLORS.GRAY}[INFO] Generated WebVTT file: ${outputPath}${COLORS.RESET}`);
  return webvttContent;

  function formatTimestamp(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    const formattedMinutes = minutes.toString().padStart(2, '0');
    const formattedSeconds = remainingSeconds.toFixed(3).padStart(6, '0');
    return `${formattedMinutes}:${formattedSeconds}`;
  }
}

/**
 * Uplaods file to pixeldrain drive (not FTP)
 * @param {string} filePath - The path to the file to upload
 * @param {string | undefined} apiKey
 * @returns {Promise} A promise that resolves when the file upload is successful
 * @throws {Error} If the file upload fails after 3 attempts
 */
async function uploadFileToPixeldrain(filePath, apiKey) {
  let attempts = 0;
  const maxAttempts = 3;

  while (attempts < maxAttempts) {
    attempts++;
    try {
      const fileName = path.basename(filePath);
      const apiUrl = `https://pixeldrain.com/api/file/${fileName}`;
      const fileStream = fs.createReadStream(filePath);
      console.info(`${COLORS.GRAY}[INFO] Attempt ${attempts} to upload file ${filePath}...${COLORS.RESET}`);

      const response = await fetch(apiUrl, {
        method: 'PUT',
        headers: {
          Authorization: `Basic ${Buffer.from(`:${apiKey}`).toString('base64')}`,
        },
        // @ts-ignore
        body: fileStream,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      } else {
        const data = await response.json();
        console.info(`${COLORS.CYAN}[INFO] Upload successful for: ${filePath}${COLORS.RESET}`);
        // @ts-ignore
        return data.id;
      }
    } catch (error) {
      console.error(`${COLORS.RED}[ERROR] Upload attempt ${attempts} encountered an error: ${error}${COLORS.RESET}`);
      if (attempts >= maxAttempts) {
        console.error(`${COLORS.RED}[ERROR] Giving up after ${attempts} attempts${COLORS.RESET}`);
        throw new Error(`Failed after ${attempts} attempts: ${error.message}`);
      }
      await sleep(60_000);
    }
  }
}

/**
 * Extract episode name between brackes e.g. extracts "EXAMPLE" from "[ABC] EXAMPLE [XYZ]"
 * @param {string} str - The string to extract text from
 * @returns {string} The extracted text between brackets
 */
function extractTextBetweenBrackets(str) {
  const regex = /\[([^\]]*)\]\s*(.*?)\s*\[/;
  const match = str.match(regex);
  if (match && match.length >= 3) {
    return match[2];
  }
  return '';
}

/**
 * Finds file in "app/0rss/downloads" with a given name
 * @param {string} searchString - The string to search for
 * @param {string} directory - The directory to search in
 * @returns {string} The file name that includes the search string, or undefined if not found
 */
function findFile(searchString, directory) {
  const files = fs.readdirSync(directory);

  for (const file of files) {
    // Check if this is a ToonsHub file (case-insensitive)
    const isToonsHub = file.toLowerCase().includes('toonshub') ||
                       file.match(/\.s\d+e\d+\./i);

    if (isToonsHub) {
      // For ToonsHub files, extract title and episode info differently
      const toonsHubInfo = extractToonsHubInfo(file);
      if (toonsHubInfo) {
        const toonsHubSearchString = `${toonsHubInfo.title} - ${toonsHubInfo.episode}`;
        if (toonsHubSearchString === searchString) {
          return file;
        }
      }
    } else {
      // For Erai-raws files, use the existing logic
      const extractedText = extractTextBetweenBrackets(file);
      if (extractedText.includes(searchString)) {
        return file;
      }
    }
  }
  return '';
}

/**
 * Extracts title and episode information from ToonsHub filename
 * @param {string} filename - The ToonsHub filename
 * @returns {Object|null} Object with title and episode, or null if extraction fails
 */
function extractToonsHubInfo(filename) {
  // Examples:
  // 1. "[ToonsHub] YAIBA Samurai Legend S01E03 1080p ..."
  // 2. "Kakushite.Makina-san.S01E12.Thus.Makina.Does.a.Naughty.Workout.1080p..."

  let titleMatch;

  // Format 1: [ToonsHub] prefix format
  if (filename.toLowerCase().includes('[toonshub]')) {
    const withoutPrefix = filename.replace(/\[toonshub\]\s*/i, '').trim();
    titleMatch = withoutPrefix.match(/^(.+?)\s+s(\d+)e(\d+)/i);
  }
  // Format 2: Dot-separated format
  else {
    titleMatch = filename.match(/^(.+?)\.s(\d+)e(\d+)\./i);
    if (titleMatch) {
      // Replace dots with spaces in the title
      titleMatch[1] = titleMatch[1].replace(/\./g, ' ');
    }
  }

  if (!titleMatch) {
    return null;
  }

  return {
    title: titleMatch[1].trim(),
    season: titleMatch[2],
    episode: parseInt(titleMatch[3], 10).toString() // Remove leading zeros
  };
}

/**
 * Creates a episode record on the website
 * @param {Object} primarySource - An object containing video links for different resolutions
 * @param {Object} secondarySource - An object containing video links for different resolutions
 * @param {string} animeId - The title of the anime
 * @param {number} episodeNumber - The episode number
 * @param {string} season - The season of the anime
 * @param {string} thumbnailLink - The link to the thumbnail
 * @param {Object} subtitleLinks - An object containing subtitle links for different languages
 * @param {string} markerPeriods - The marker periods JSON string
 * @param {string} thumbnailFile - The thumbnail file link
 * @returns {Promise<any>} A promise that resolves to the response data from the API
 * @throws {Error} If the episode creation fails after 3 attempts
 */

async function createEpisode(
  primarySource,
  secondarySource,
  animeId,
  episodeNumber,
  season,
  thumbnailLink,
  subtitleLinks,
  markerPeriods,
  thumbnailFile,
  previewFile
) {
  try {
    // Get anime title from database
    const metadata = await getAnimeMetadata(animeId);
    if (!metadata) {
      throw new Error(`No metadata found for AniList ID ${animeId}`);
    }
    const animeTitle = metadata.romaji_title.replaceAll(' ', '_');

    // Get MAL ID from existing record or from AniList
    let malId = null;
    try {
      const query = `
        query ($id: Int) {
          Media(id: $id, type: ANIME) {
            idMal
          }
        }
      `;

      const response = await fetch('https://graphql.anilist.co', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          query: query,
          variables: { id: animeId }
        })
      });

      const data = await response.json();
      // @ts-ignore
      malId = data?.data?.Media?.idMal || null;

      if (malId) {
        console.info(`${COLORS.CYAN}[INFO] Found MAL ID ${malId} for AniList ID ${animeId}${COLORS.RESET}`);
      }
    } catch (error) {
      console.error(`${COLORS.RED}[ERROR] Failed to fetch MAL ID: ${error.message}${COLORS.RESET}`);
    }

    // Get episode title
    const episodeTitle = await updateEpisodeTitle(malId, animeId, episodeNumber);

    const { data: newEpisode, error } = await supabase
      .from('anime')
      .insert([
        {
          episode_number: episodeNumber,
          anime_title: animeTitle,
          anilist_id: animeId,
          primary_source: primarySource,
          secondary_source: secondarySource,
          season: season,
          thumbnail_link: thumbnailLink,
          markerPeriods: markerPeriods,
          thumbnailFile: thumbnailFile,
          subtitleLinks: subtitleLinks,
          preview_file: previewFile,
          mal_id: malId,
          episode_title: episodeTitle
        }
      ])
      .select();

    if (error) {
      throw new Error(JSON.stringify(error));
    }

    console.info(
      `${COLORS.CYAN}[INFO] Created new episode for ${animeTitle} (AniList ID ${animeId}) - ${episodeNumber}${COLORS.RESET}`
    );

    await webhookClient.send({
      content: `[INFO] Created new episode for ${animeTitle} (AniList ID ${animeId}) - ${episodeNumber}`
    });

    return newEpisode;
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] An error occurred during episode creation! - ${error.message}${COLORS.RESET}`);
    throw error;
  }
}

async function deleteAllFilesInDirectory(directoryPath) {
  try {
    const files = fs.readdirSync(directoryPath).filter((file) => file !== '.gitkeep' && !file.endsWith('.js'));
    for (const file of files) {
      const filePath = path.join(directoryPath, file);
      try {
        fs.unlinkSync(filePath);
        console.log(`${COLORS.GRAY}[INFO] Deleted file: ${filePath}${COLORS.RESET}`);
      } catch (error) {
        console.error(`${COLORS.RED}[ERROR] Error deleting file ${filePath}: ${error.message}${COLORS.RESET}`);
      }
    }
    console.info(`${COLORS.GRAY}[INFO] Completed deletion process for directory: ${directoryPath}${COLORS.RESET}`);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error processing directory: ${directoryPath}${COLORS.RESET}`);
    console.error(error);
  }
}

/**
 * Creates the episode thumbnail taking one frame from 06:17 minutes into the file
 * @param {string} videoPath - The path to the video file
 * @param {string} filename - The name of the file
 * @returns {Promise<string>} A promise that resolves to the path of the created thumbnail
 * @throws {Error} If there is an error creating the thumbnail
 */
async function createThumbnail(videoPath, filename) {
  try {
    const title = extractTitleFromFileName(filename);
    const episodeNumber = extractEpisodeNumberFromFileName(filename);
    const fullTitle = `[lycoris.cafe] ${title} - ${episodeNumber}`;
    const framePath = `5upload/final/${fullTitle}.webp`;

    console.info(`${COLORS.CYAN}[INFO] Creating episode thumbnail...${COLORS.RESET}`);

    await new Promise((resolve, reject) => {
      ffmpeg(videoPath)
        .screenshots({
          timestamps: ['00:17'],
          filename: `${fullTitle}.webp`,
          folder: '5upload/final',
        })
        .on('end', () => {
          resolve('Screenshot taken successfully');
        })
        .on('error', (err) => {
          reject('Error taking screenshot: ' + err);
        });
    });

    return framePath;
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error creating thumbnail: ${COLORS.RESET}`, error);
    throw error;
  }
}

/**
 * Executes a given command with parameters
 * @param {string} command - The command to execute
 * @param {string[]} args - The arguments for the command
 * @returns {Promise<void>} A promise that resolves when the command execution is successful
 * @throws {Error} If the command execution fails
 */
async function executeCommand(command, args, p0) {
  return new Promise((resolve, reject) => {
    const process = cp.spawn(command, args, { windowsHide: true, shell: true });
    console.log(args.join(' '));

    process.stdout.on('data', (data) => console.log(data.toString()));
    process.stderr.on('data', (data) => console.log(data.toString()));

    process.on('exit', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Process exited with code ${code}`));
      }
    });
  });
}

/**
 * Exxtracts OP and ED times from a file
 * First tries to extract them directly fromn the .mkv container metadata, if that fails
 * It extracts the approximate timestamps based on the placement of subtitles
 * If there is at leat 80 seconds gap in the last and next subtitle line - it takes that as an segment
 * and substracts 5 seconds because it's better to undershoot then overshoot
 * @param {string} assFile - The path to the ASS subtitle file
 * @param {string} sourceFilePath - The path to the source video file
 * @returns {Promise<Object[]>} A promise that resolves to an array of marker periods
 */
async function markOPED(assFile, sourceFilePath) {
  let periods = [];
  // @ts-ignore
  periods = await extractOPEDFromMetadata(sourceFilePath);
  if (!periods || periods.length === 0) {
    periods = await findOPEDfromSubtitles(assFile, sourceFilePath);
  }
  if (!periods || periods.length === 0) {
    periods = [
      { startTime: '00:00:00.00', endTime: '00:00:00.00' },
      { startTime: '00:00:00.00', endTime: '00:00:00.00' },
    ];
  }

  /**
   * Tries to extract OP/ED timestamps from .mkv container metadata
   * @param {string} sourceFilePath - The path to the source video file
   * @returns {Promise<Object[]|null>} A promise that resolves to an array of marker periods or null if an error occurs
   */
  async function extractOPEDFromMetadata(sourceFilePath) {
    const chapterTitles = ['Opening', 'Episode', 'Credits Start', 'Credits End'];
    try {
      const command = `mkvinfo "${sourceFilePath}"`;
      const { stdout } = await execAsync(command);
      const lines = stdout.split('\r\n');

      let chapterData = [];
      let currentChapter = {};
      let timeStartMatch = null;
      let timeStartExists = false;

      for (let line of lines) {
        if (line.includes('Chapter time start') || timeStartExists) {
          if (!timeStartExists) {
            timeStartMatch = line.match(/Chapter time start: (.*)/);
            timeStartExists = true;
          }
          if (line.includes('Chapter string')) {
            const titleMatch = line.match(/Chapter string: (.*)/);
            if (titleMatch) {
              const title = titleMatch[1].trim();
              // Check if the title matches any of the titles we are interested in
              if (chapterTitles.includes(title)) {
                currentChapter.title = title;
              } else {
                timeStartMatch = null;
                timeStartExists = false;
              }
            }
          }
          if (currentChapter.title && timeStartMatch) {
            //Only one of those two pairs can be true at the same time
            if (currentChapter.title === 'Opening') currentChapter.startTime = formatTime(timeStartMatch[1]);
            if (currentChapter.title === 'Episode') currentChapter.endTime = formatTime(timeStartMatch[1]);

            if (currentChapter.title === 'Credits Start') currentChapter.startTime = formatTime(timeStartMatch[1]);
            if (currentChapter.title === 'Credits End') currentChapter.endTime = formatTime(timeStartMatch[1]);

            // This menas there is no Opening in the file.
            if (currentChapter.title === 'Episode' && !currentChapter.startTime) {
              chapterData.push({
                startTime: '00:00:00',
                endTime: '00:00:00',
              });
              timeStartExists = false;
              timeStartMatch = null;
              currentChapter = {};
            }
            if (currentChapter.startTime && currentChapter.endTime) {
              chapterData.push({
                startTime: currentChapter.startTime,
                endTime: currentChapter.endTime,
              });
              timeStartExists = false;
              timeStartMatch = null;
              currentChapter = {};
            }
            if (
              currentChapter.title === 'Opening' ||
              currentChapter.title === 'Episode' ||
              currentChapter.title === 'Credits Start' ||
              currentChapter.title === 'Credits End'
            ) {
              timeStartExists = false;
              timeStartMatch = null;
              currentChapter.title = null;
            }
          }
        }
      }

      return chapterData;
    } catch (error) {
      console.error('Error processing MKV file:', error);
      return null;
    }

    /**
     * Formats the time received from .mkv container metadata
     * @param {string} mkvTime - The time string in MKV format
     * @returns {string} The formatted time string
     */
    function formatTime(mkvTime) {
      return mkvTime.replace(/\.\d+$/, '.00'); // Remove milliseconds for format consistency
    }
  }

  /**
   * Fallback function to find approximate OP/ED timestamps using subtitle file.
   * For details of implementation see above.
   * @param {string} assFile - The path to the ASS subtitle file
   * @param {string} sourceFilePath - The path to the source video file
   * @returns {Promise<Object[]>} A promise that resolves to an array of marker periods
   */
  async function findOPEDfromSubtitles(assFile, sourceFilePath) {
    const lines = fs.readFileSync(assFile, 'utf8').split('\n');
    const chapterData = [];
    let lastStartTime = 0;
    let lastStartTimestamp = '';
    const songRegex = /song/;
    const titleRegex = /title/;

    for (const line of lines) {
      if (line.startsWith('Dialogue:')) {
        const parts = line.split(',');
        const style = parts[3].trim();
        const endTimestamp = parts[1].trim();
        const startTimestamp = parts[2].trim();
        if (!matchesRegex(style, songRegex) && !matchesRegex(style, titleRegex)) {
          const endTime = parseTimestamp(endTimestamp);
          const startTime = parseTimestamp(startTimestamp);
          if (endTime - lastStartTime >= 80) {
            chapterData.push({
              endTime: formatTimestamp(endTime - 5) + '.00',
              startTime: formatTimestamp(lastStartTime) + '.00',
            });
          }
          lastStartTime = startTime;
          lastStartTimestamp = startTimestamp;
        }
      }
    }

    // Process the last subtitle line
    if (chapterData.length < 2) {
      const videoDuration = await getVideoDuration(`${sourceFilePath}`);
      chapterData.push({
        startTime: formatTimestamp(lastStartTime) + '.00',
        endTime: videoDuration + '.00',
      });
    }
    // if periods.length STILL less than 2 (means only one segment 'ending' was added)
    // pad the length to not cause any erros
    if (chapterData.length < 2) {
      chapterData.push({
        startTime: '00:00:00.00',
        endTime: '00:00:00.00',
      });
    }

    /**
     * Takes in .ass timestamp and return it's value in seconds
     * @param {string} timestamp - The timestamp string
     * @returns {number} The parsed timestamp in seconds
     */
    function parseTimestamp(timestamp) {
      const parts = timestamp.split(':');
      const hours = parseInt(parts[0], 10);
      const minutes = parseInt(parts[1], 10);
      const seconds = parseFloat(parts[2].replace(',', '.'));
      return hours * 3600 + minutes * 60 + seconds;
    }

    /**
     * Takes in the time in seconds and returns it as a timestamp in the fomrat of "<HOURS>:<MINUTES>:<SECONDS>" with leading zeroes
     * @param {number} time - The time in seconds
     * @returns {string} The formatted timestamp string
     */
    function formatTimestamp(time) {
      const hours = Math.floor(time / 3600);
      const minutes = Math.floor((time % 3600) / 60);
      const seconds = Math.floor(time % 60);
      return `${padZero(hours)}:${padZero(minutes)}:${padZero(seconds)}`;
      function padZero(num) {
        return num.toString().padStart(2, '0');
      }
    }

    /**
     * Checks if a given regex returns a result
     * @param {string} str - The string to check
     * @param {RegExp} regex - The regex pattern
     * @returns {boolean} True if the string matches the regex pattern, false otherwise
     */
    function matchesRegex(str, regex) {
      return regex.test(str.toLowerCase());
    }

    /**
     * Gets the duration of a video file using fluent-ffmpeg and ffprobe
     * @param {string} sourceFilePath - The path to the source video file
     * @returns {Promise<string>} A promise that resolves to the formatted video duration
     */
    function getVideoDuration(sourceFilePath) {
      return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(sourceFilePath, (err, metadata) => {
          if (err) {
            reject(err);
            return;
          }
          // @ts-ignore
          const duration = secondsToTimestamp(metadata.format.duration);
          resolve(duration);
        });
      });

      /**
       * Converts seconds to timestamp in the fomrat of "<HOURS>:<MINUTES>:<SECONDS>" with leading zeroes
       * @param {number} seconds - The number of seconds
       * @returns {string} The formatted timestamp string
       */
      function secondsToTimestamp(seconds) {
        const hours = Math.floor(seconds / 3600); // Get whole hours
        seconds %= 3600; // Remaining seconds after extracting hours
        const minutes = Math.floor(seconds / 60); // Get remaining minutes
        seconds = Math.floor(seconds % 60); // Remaining seconds after extracting minutes

        // Formatting hours, minutes and seconds with leading zeros
        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');
        const formattedSeconds = seconds.toString().padStart(2, '0');

        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
      }
    }
    return chapterData;
  }
  return periods;
}

function addKaraoke(subtitleFile, karaokeFile, offset) {
  /* === OFFSETING KARAOKE FILE */
  const content = fs.readFileSync(karaokeFile, 'utf8');
  const lines = content.split('\n');

  const offsetMs = parseTimecode(offset);

  const offsetLines = lines.map((line) => {
    if (line.startsWith('Dialogue:')) {
      const parts = line.split(',');
      if (parts.length >= 2) {
        const startTime = parseTimecode(parts[1]);
        const endTime = parseTimecode(parts[2]);

        const offsetStart = formatTimecode(startTime + offsetMs);
        const offsetEnd = formatTimecode(endTime + offsetMs);

        parts[1] = offsetStart;
        parts[2] = offsetEnd;

        return parts.join(',');
      }
    }
    return line;
  });

  /* === INSERTING IT INTO THE MAIN FILE === */

  const preparedKaraoke = offsetLines.join('\n');
  try {
    // Read the input file
    let content = fs.readFileSync(subtitleFile, 'utf8');

    // Split the content into lines
    let lines = content.split('\n');

    // Find the insertion point
    let insertionIndex = lines.findIndex((line) => {
      if (line.startsWith('Dialogue:')) {
        let parts = line.split(',');
        if (parts.length >= 2) {
          let lineTimestamp = parts[1].trim();
          return lineTimestamp >= offset;
        }
      }
      return false;
    });

    // If no suitable insertion point found, append to the end
    if (insertionIndex === -1) {
      insertionIndex = lines.length;
    }

    // Insert the prepared karaoke at the found index
    lines.splice(insertionIndex, 0, ...preparedKaraoke.split('\n'));

    // Join the lines back together
    let newContent = lines.join('\n');

    // Write the new content to the output file
    fs.writeFileSync(subtitleFile, newContent, 'utf8');

    console.log(`Karaoke inserted successfully. Output saved to ${subtitleFile}`);
  } catch (error) {
    console.error('An error occurred:', error);
  }

  // Helper function to parse timecode string to milliseconds
  function parseTimecode(timecode) {
    const [hours, minutes, seconds] = timecode.split(':');
    return (parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseFloat(seconds)) * 1000;
  }

  // Helper function to format milliseconds to timecode string
  function formatTimecode(ms) {
    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = (totalSeconds % 60) + (ms % 1000) / 1000;
    return `${padZero(hours)}:${padZero(minutes)}:${padZero(seconds.toFixed(2))}`;
  }

  // Helper function to pad single digits with leading zero
  function padZero(num) {
    return num.toString().padStart(2, '0');
  }
}

async function embedInfoToDiscord(content) {
  content = `\`\`\`html
<iframe src="${content}" width="620" height="348" style="border:none;" scrolling="no" allowfullscreen></iframe>
\`\`\`
\`\`\`
Grupa lycoris.cafe
\`\`\`
\`\`\`
https://lycoris.cafe
\`\`\`
`;
  try {
    const webhookURL = process.env.DISCORD_EMBED_WEBHOOK;
    const payload = { content };
    // @ts-ignore
    const response = await axios.post(webhookURL, payload);
    if (response.status !== 204) {
      console.error('Failed to send Discord webhook. Status code:', response.status);
    }
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error sending Discord webhook:${COLORS.RESET}`, error.message);
  }
}

async function isEpisodeAlreadyCreated(animeId, episodeNumber) {
  let { data: anime, error } = await supabase
    .from('anime')
    .select('id')
    .eq('anilist_id', animeId)
    .eq('episode_number', episodeNumber);
  return anime?.length !== 0 ? true : false;
}

async function updateEpisode(animeId, episodeNumber, secondarySource, subtitleLinks, previewFile) {
  // Get MAL ID from existing record
  const { data: existingAnime, error: fetchError } = await supabase
    .from('anime')
    .select('mal_id')
    .eq('anilist_id', animeId)
    .eq('episode_number', episodeNumber)
    .single();

  if (fetchError) {
    console.error(`${COLORS.RED}[ERROR] Failed to fetch existing anime record: ${fetchError.message}${COLORS.RESET}`);
    throw fetchError;
  }

  // Get episode title
  const episodeTitle = await updateEpisodeTitle(existingAnime?.mal_id, animeId, episodeNumber);

  const { data: anime, error: updateError } = await supabase
    .from('anime')
    .update({
      primary_source: {
        "HD": "",
        "SD": "",
        "FHD": "",
        "preview": "",
        "SourceMKV": ""
      },
      secondary_source: secondarySource,
      subtitleLinks: subtitleLinks,
      preview_file: previewFile,
      needs_update: false,
      episode_title: episodeTitle,
      burst_source: 'update_me'
    })
    .eq('anilist_id', animeId)
    .eq('episode_number', episodeNumber)
    .select();

  if (updateError) {
    throw new Error(JSON.stringify(updateError));
  }

  if (!anime || anime.length === 0) {
    throw new Error(
      `Episode not found in database :(. It might still be uploading or the filename might be incorrect. Tried to find AniList ID ${animeId} - ${episodeNumber}.)`
    );
  }

  console.info(
    `${COLORS.CYAN}[INFO] Updated episode for AniList ID ${animeId} - ${episodeNumber}${COLORS.RESET}`
  );

  await webhookClient.send({
    content: `[INFO] Updated episode for AniList ID ${animeId} - ${episodeNumber}`
  });
}

async function updateMALIds() {
  try {
    // Fetch all entries with anilist_id but no mal_id
    const { data: animeEntries, error: fetchError } = await supabase
      .from('anime')
      .select('id, anilist_id, mal_id')
      .not('anilist_id', 'is', null)
      .is('mal_id', null);

    if (fetchError) {
      console.error(`${COLORS.RED}[ERROR] Failed to fetch anime entries: ${fetchError.message}${COLORS.RESET}`);
      return;
    }

    if (!animeEntries || animeEntries.length === 0) {
      console.info(`${COLORS.GRAY}[INFO] No entries need MAL ID updates${COLORS.RESET}`);
      return;
    }

    for (const entry of animeEntries) {
      try {
        const query = `
          query ($id: Int) {
            Media(id: $id, type: ANIME) {
              idMal
            }
          }
        `;

        const response = await fetch('https://graphql.anilist.co', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify({
            query: query,
            variables: { id: entry.anilist_id }
          })
        });

        const data = await response.json();
        // @ts-ignore
        const malId = data?.data?.Media?.idMal || null;

        if (malId) {
          const { error: updateError } = await supabase
            .from('anime')
            .update({ mal_id: malId })
            .eq('id', entry.id);

          if (updateError) {
            console.error(`${COLORS.RED}[ERROR] Failed to update MAL ID for entry ${entry.id}: ${updateError.message}${COLORS.RESET}`);
          } else {
            console.info(`${COLORS.CYAN}[INFO] Updated MAL ID ${malId} for entry ${entry.id}${COLORS.RESET}`);
          }
        }

        // Add a small delay to avoid hitting AniList's rate limit
        await sleep(1000);
      } catch (error) {
        console.error(`${COLORS.RED}[ERROR] Failed to process entry ${entry.id}: ${error.message}${COLORS.RESET}`);
      }
    }
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Failed to update MAL IDs: ${error.message}${COLORS.RESET}`);
  }
}

function getAnilistId(title) {
  // First try direct match
  if (titleMapping[title]) {
    return titleMapping[title];
  }

  // Try matching with spaces replaced by underscores
  const underscoreTitle = title.replace(/ /g, '_');
  if (titleMapping[underscoreTitle]) {
    return titleMapping[underscoreTitle];
  }

  // If no match found, log warning and return null
  console.warn(`${COLORS.YELLOW}[WARNING] No AniList ID found for title: ${title}${COLORS.RESET}`);
  return null;
}