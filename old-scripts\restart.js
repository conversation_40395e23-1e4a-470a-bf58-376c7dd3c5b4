/**
 * @file Restart Command for Discord Bot
 * @description This script implements a Discord bot command for restarting specific services.
 * Key features include:
 * - Environment configuration using dotenv
 * - Integration with Supabase (though not used in this command)
 * - Discord slash command setup with options
 * - User authentication for command usage
 * - Service restart functionality using PM2
 * - Error handling and user feedback
 *
 * The command allows restarting two services:
 * 1. Download service
 * 2. Upload service (referred to as 'index' in PM2)
 *
 * @requires dotenv
 * @requires child_process
 * @requires @discordjs/builders
 * @requires @supabase/supabase-js
 */

import { config } from 'dotenv';
config();
import * as cp from 'child_process';

import { SlashCommandBuilder } from '@discordjs/builders';
import { createClient } from '@supabase/supabase-js';

// Supabase configuration (not used in this command, but kept for potential future use)
const supabaseUrl = 'https://zglyjsqsvevnyudbazgy.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * @enum {string}
 * @description Enum for restart options
 */
const RestartOption = {
  /** Option to restart the download service */
  DOWNLOAD: 'download',
  /** Option to restart the upload service */
  UPLOAD: 'upload',
};

/**
 * @type {SlashCommandBuilder}
 * @description Defines the structure of the /restart slash command
 */
export const data = new SlashCommandBuilder()
  .setName('restart')
  .setDescription('🦌')
  .addStringOption((option) =>
    option
      .setName('service')
      .setDescription('🦌')
      .setRequired(true)
      .addChoices({ name: 'Download', value: RestartOption.DOWNLOAD }, { name: 'Upload', value: RestartOption.UPLOAD })
  );

/**
 * @async
 * @function run
 * @description Handles the execution of the /restart command
 * @param {Object} params - The parameters object
 * @param {Object} params.interaction - The Discord interaction object
 * @param {Object} params.client - The Discord client object
 * @param {Object} params.handler - The command handler object
 * @returns {Promise<void>}
 */
export async function run({ interaction, client, handler }) {
  // User authentication
  if (interaction.user.id !== '351006685587963916' && interaction.user.id !== '185126303677153281') {
    await interaction.reply({
      content: 'Nie możesz użyć tej komendy.',
      ephemeral: true,
    });
    return;
  }

  const chosenOption = interaction.options.getString('service');

  // Determine the appropriate PM2 command based on the chosen option
  let command;
  switch (chosenOption) {
    case RestartOption.DOWNLOAD:
      command = 'pm2 restart download';
      break;
    case RestartOption.UPLOAD:
      command = 'pm2 restart index';
      break;
    default:
      await interaction.reply({
        content: 'Nieprawidłowa opcja.',
        ephemeral: true,
      });
      return;
  }

  try {
    // Execute the PM2 restart command
    cp.execSync(command, { stdio: 'inherit' });
    await interaction.reply({
      content: `Pomyślnie zrestartowano usługę: ${chosenOption}`,
      ephemeral: true,
    });
  } catch (error) {
    console.error(`Error restarting service: ${error}`);
    await interaction.reply({
      content: 'Wystąpił błąd podczas restartowania usługi.',
      ephemeral: true,
    });
  }
}

/**
 * @type {Object}
 * @property {boolean} devOnly - Indicates if the command is for developers only
 * @property {boolean} deleted - Indicates if the command has been deleted
 */
export const options = {
  devOnly: true,
  deleted: false,
};
