import { createClient } from '@supabase/supabase-js';
import cron from 'node-cron';
import dotenv from 'dotenv';
import axios from 'axios';

dotenv.config();

// Configuration
const BACKTRACK_DAYS = 7;
const BATCH_SIZE = 50;
const JIKAN_RATE_LIMIT_MS = 1500;  // Jikan requires 1s between requests
const ANILIST_RATE_LIMIT_MS = 2500; // AniList rate limit

const supabase = createClient(
  process.env.SUPABASE_PROJECT_URL,
  process.env.SUPABASE_ADMIN_KEY
);

const DISCORD_WEBHOOK_URL = process.env.CRON_WEBHOOK;

const rateLimit = {
  async waitForSlot(ms) {
    await new Promise(resolve => setTimeout(resolve, ms));
  }
};

async function notifyDiscord(message) {
  try {
    const embed = {
      title: "Update Error",
      description: message,
      color: 15158332,
      timestamp: new Date().toISOString()
    };

    await fetch(DISCORD_WEBHOOK_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ embeds: [embed] })
    });
  } catch (error) {
    console.error('Failed to send Discord notification:', error);
  }
}

async function getAnimeEpisodes(malId) {
  try {
    const allEpisodes = [];
    let hasNextPage = true;
    let currentPage = 1;

    while (hasNextPage) {
      const response = await axios.get(
        `https://api.jikan.moe/v4/anime/${malId}/episodes?page=${currentPage}`
      );

      allEpisodes.push(...response.data.data);
      hasNextPage = response.data.pagination.has_next_page;
      currentPage++;

      await rateLimit.waitForSlot(JIKAN_RATE_LIMIT_MS);
    }

    return allEpisodes;
  } catch (error) {
    console.error(`Error fetching episodes for MAL ID ${malId}:`, error.message);
    return null;
  }
}

async function getAniListEpisodes(anilistId) {
  try {
    const response = await fetch('https://graphql.anilist.co', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query ($id: Int) {
            Media(id: $id) {
              episodes
              streamingEpisodes {
                title
                site
                url
              }
            }
          }
        `,
        variables: { id: anilistId }
      })
    });

    const data = await response.json();
    if (data.errors) return null;

    return data.data.Media.streamingEpisodes;
  } catch (error) {
    console.error(`Error fetching AniList episodes for ID ${anilistId}:`, error.message);
    return null;
  }
}

// Counter for airing schedule updates
let airingScheduleUpdateCount = 0;

// In the processAiringSchedule function, combine previous and next episodes
async function processAiringSchedule(animeData) {
  try {
    const anilistId = animeData.id;
    const airingNodes = animeData.airingSchedule?.nodes || [];
    const nextAiringEpisode = animeData.nextAiringEpisode;

    // Combine past episodes and next episode
    let allEpisodes = [...airingNodes];
    if (nextAiringEpisode && !airingNodes.some(node => node.id === nextAiringEpisode.id)) {
      allEpisodes.push(nextAiringEpisode);
    }

    if (allEpisodes.length === 0) return;

    // Sort episodes by episode number
    allEpisodes.sort((a, b) => a.episode - b.episode);

    // Format all episodes data consistently
    const allEpisodesData = allEpisodes.map(ep => ({
      id: ep.id,
      episode: ep.episode,
      airingAt: ep.airingAt,
      timeUntilAiring: ep.airingAt > Math.floor(Date.now() / 1000) ? ep.timeUntilAiring : 0
    }));

    // Group episodes into next and previous
    const now = Math.floor(Date.now() / 1000);
    const futureEpisodes = allEpisodes.filter(ep => ep.airingAt > now);

    // Get the next episode (if any)
    const nextEpisode = futureEpisodes.length > 0 ? futureEpisodes[0] : null;

    // Format data for database
    if (nextEpisode) {
      // First check if a record already exists for this anime
      const { data: existingRecord, error: selectError } = await supabase
        .from('airing_schedule')
        .select('id')
        .eq('anilist_id', anilistId)
        .single();

      let error;

      if (selectError && selectError.code !== 'PGRST116') { // PGRST116 means no rows returned, so insert
        console.error(`Error checking existing airing schedule for ID ${anilistId}:`, selectError);
        error = selectError;
      } else {
        const scheduleData = {
          anilist_id: anilistId,
          next_episodes: allEpisodesData.length > 0 ? allEpisodesData : null,
        };

        if (existingRecord) {
          // Update existing record
          const { error: updateError } = await supabase
            .from('airing_schedule')
            .update(scheduleData)
            .eq('id', existingRecord.id);
          error = updateError;
        } else {
          // Insert new record
          const { error: insertError } = await supabase
            .from('airing_schedule')
            .insert(scheduleData);
          error = insertError;
        }
      }

      if (error) {
        console.error(`Error updating airing schedule for ID ${anilistId}:`, error);
      } else {
        airingScheduleUpdateCount++;
        console.log(`Updated airing schedule for ID ${anilistId}, next episode: ${nextEpisode.episode}`);
      }
    }
  } catch (error) {
    console.error(`Error processing airing schedule for anime ID ${animeData.id}:`, error.message);
  }
}

async function updateData() {
  const startTime = new Date();
  let updatedCount = 0;
  let errorCount = 0;
  let episodeUpdateCount = 0;

  // Reset the airing schedule update counter
  airingScheduleUpdateCount = 0;

  try {
    console.log('Starting update:', startTime.toISOString());

    // Get episodes needing title updates
    const backtrackDate = new Date();
    backtrackDate.setDate(backtrackDate.getDate() - BACKTRACK_DAYS);

    const { data: episodesNeedingTitles, error: episodeError } = await supabase
      .from('anime')
      .select('mal_id, anilist_id, episode_number')
      .is('episode_title', null)
      .gte('date_added', backtrackDate.toISOString());

    if (episodeError) {
      console.error('Error fetching episodes needing titles:', episodeError);
      await notifyDiscord(`Error fetching episodes needing titles: ${episodeError.message}`);
    }

    // Update episode titles using Jikan API
    console.log('Starting Jikan API pass...');
    const animeMap = {};
    episodesNeedingTitles?.forEach(episode => {
      if (!animeMap[episode.mal_id]) {
        animeMap[episode.mal_id] = {
          episodes: [],
          anilist_id: episode.anilist_id
        };
      }
      animeMap[episode.mal_id].episodes.push(episode);
    });

    for (const malId in animeMap) {
      try {
        const episodes = await getAnimeEpisodes(malId);
        if (!episodes) continue;

        for (const episode of episodes) {
          const title = episode.title.replace(/^Episode \d+[:\s-]*\s*/i, '').trim();

          const { error: updateError } = await supabase
            .from('anime')
            .update({ episode_title: title })
            .match({
              mal_id: malId,
              episode_number: episode.mal_id
            });

          if (updateError) {
            errorCount++;
            await notifyDiscord(`Episode title update error for anime ${malId}: ${updateError.message}`);
          } else {
            episodeUpdateCount++;
          }
        }
      } catch (animeError) {
        errorCount++;
        await notifyDiscord(`Failed to process MAL ID ${malId}: ${animeError.message}`);
      }
    }

    // Second pass with AniList API for remaining episodes
    console.log('Starting AniList API pass...');
    const { data: remainingEpisodes, error: remainingError } = await supabase
      .from('anime')
      .select('mal_id, anilist_id, episode_number')
      .is('episode_title', null)
      .gte('date_added', backtrackDate.toISOString());

    if (remainingError) {
      console.error('Error fetching remaining episodes:', remainingError);
      await notifyDiscord(`Error fetching remaining episodes: ${remainingError.message}`);
    } else if (remainingEpisodes?.length > 0) {
      const anilistMap = {};
      remainingEpisodes.forEach(episode => {
        if (!anilistMap[episode.anilist_id]) {
          anilistMap[episode.anilist_id] = [];
        }
        anilistMap[episode.anilist_id].push(episode);
      });

      for (const anilistId in anilistMap) {
        try {
          await rateLimit.waitForSlot(ANILIST_RATE_LIMIT_MS);
          const episodes = await getAniListEpisodes(anilistId);
          if (!episodes) continue;

          for (const episode of episodes) {
            let episodeNumber = null;
            const titleMatch = episode.title.match(/Episode\s*(\d+)/i);
            const urlMatch = episode.url.match(/episode-(\d+)/i);

            if (titleMatch) {
              episodeNumber = parseInt(titleMatch[1]);
            } else if (urlMatch) {
              episodeNumber = parseInt(urlMatch[1]);
            }

            if (episodeNumber) {
              const title = episode.title
                .replace(/^Episode \d+[:\s-]*\s*/i, '')
                .replace(/^[-\s]*/, '')
                .trim();

              const { error: updateError } = await supabase
                .from('anime')
                .update({ episode_title: title })
                .match({
                  anilist_id: parseInt(anilistId),
                  episode_number: episodeNumber
                });

              if (updateError) {
                errorCount++;
                await notifyDiscord(`AniList episode title update error for anime ${anilistId}: ${updateError.message}`);
              } else {
                episodeUpdateCount++;
              }
            }
          }
        } catch (animeError) {
          errorCount++;
          await notifyDiscord(`Failed to process AniList ID ${anilistId}: ${animeError.message}`);
        }
      }
    }

    // Get all anime metadata for AniList update
    const { data: metadata, error: metadataError } = await supabase
      .from('anime_metadata')
      .select('anilist_id');

    if (metadataError) throw metadataError;

    const anilistIds = metadata.map(m => m.anilist_id);
    const totalBatches = Math.ceil(anilistIds.length / BATCH_SIZE);

    for (let i = 0; i < anilistIds.length; i += BATCH_SIZE) {
      const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
      const batchIds = anilistIds.slice(i, i + BATCH_SIZE);

      try {
        await rateLimit.waitForSlot(ANILIST_RATE_LIMIT_MS);

        const response = await fetch('https://graphql.anilist.co', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify({
            query: `
              query ($ids: [Int]) {
                Page {
                  media(id_in: $ids) {
                    id
                    averageScore
                    popularity
                    trending
                    favourites
                    status
                    episodes
                    airingSchedule(notYetAired: false) {
                      nodes {
                        id
                        airingAt
                        timeUntilAiring
                        episode
                      }
                    }
                    nextAiringEpisode {
                      id
                      airingAt
                      timeUntilAiring
                      episode
                    }
                    rankings {
                      id
                      rank
                      type
                      format
                      year
                      season
                      allTime
                      context
                    }
                  }
                }
              }
            `,
            variables: { ids: batchIds }
          })
        });

        const data = await response.json();

        if (data.errors) {
          errorCount++;
          await notifyDiscord(`Batch ${batchNumber}/${totalBatches} GraphQL Errors: ${JSON.stringify(data.errors)}`);
          continue;
        }

        // First, process airing schedule data for all items
        for (const item of data.data.Page.media) {
          await processAiringSchedule(item);
        }

        // Then, prepare the rankings data
        const updatedData = data.data.Page.media.map(item => {
          const rankings = item.rankings?.map(rank => ({
            id: rank.id,
            rank: rank.rank,
            type: rank.type,
            format: rank.format,
            year: rank.year,
            season: rank.season,
            allTime: rank.allTime,
            context: rank.context
          })) || [];

          return {
            anilist_id: item.id,
            popularity: item.popularity,
            average_score: item.averageScore,
            trending: item.trending,
            favourites: item.favourites,
            status: item.status,
            rankings: rankings,
            last_updated: new Date().toISOString()
          };
        });

        const { error: upsertError } = await supabase
          .from('anime_rankings')
          .upsert(updatedData, {
            onConflict: 'anilist_id'
          });

        if (upsertError) {
          errorCount++;
          await notifyDiscord(`Batch ${batchNumber}/${totalBatches} Rankings Upsert Error: ${upsertError.message}`);
          continue;
        }

        updatedCount += updatedData.length;
        console.log(`Updated ${updatedData.length} rankings in batch ${batchNumber}/${totalBatches}`);

      } catch (batchError) {
        errorCount++;
        await notifyDiscord(`Batch ${batchNumber}/${totalBatches} Failed: ${batchError.message}`);
      }
    }

    const duration = Math.round((new Date() - startTime) / 1000);
    const summary = `Update completed:\n` +
      `Updated Rankings: ${updatedCount} entries\n` +
      `Updated Episode Titles: ${episodeUpdateCount} entries\n` +
      `Updated Airing Schedules: ${airingScheduleUpdateCount} entries\n` +
      `Errors: ${errorCount}\n` +
      `Duration: ${duration} seconds`;

    console.log(summary);
    if (errorCount > 0) {
      await notifyDiscord(summary);
    }

  } catch (error) {
    const errorMessage = `Critical error updating data: ${error.message}`;
    console.error(errorMessage);
    await notifyDiscord(errorMessage);
  }
}

// Schedule cron job to run once every day
cron.schedule('0 5 * * *', async () => {
  console.log('Running daily update');
  await updateData();
});

// Initial run on startup
updateData();

// Handle process errors and shutdown
process.on('SIGINT', () => {
  console.log('Shutting down updater');
  process.exit(0);
});

process.on('uncaughtException', async (error) => {
  await notifyDiscord(`Uncaught Exception: ${error.message}`);
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', async (reason) => {
  await notifyDiscord(`Unhandled Rejection: ${reason}`);
  console.error('Unhandled Rejection:', reason);
});