/**
 * @file Reverse Season Mapping Utility
 * @description This script provides functionality to reverse-transform lycoris.cafe anime titles
 * back to their original format for torrent searching. It handles season mapping transformations
 * where episode numbers need to be converted back to their original season context.
 * 
 * Example: "lycoris.cafe_Sono_Bisque_Doll_wa_<PERSON><PERSON>_wo_Suru_Season_2_-_01.ass"
 * Should become: title="Sono Bisque Doll wa Koi wo Suru", episode="13"
 */

import fs from 'fs';
import path from 'path';

// Load the SubsPlease season mapping configuration
let seasonMappings = {};
try {
  const mappingPath = path.resolve('./_resources/subsplease_season_mapping.json');
  const mappingData = fs.readFileSync(mappingPath, 'utf8');
  const config = JSON.parse(mappingData);
  seasonMappings = config.mappings || {};
} catch (error) {
  console.warn('[WARN] Could not load SubsPlease season mapping configuration:', error.message);
  // Initialize with empty mappings if file doesn't exist
  seasonMappings = {};
}

/**
 * Extracts anime title and episode number from lycoris.cafe filename
 * @param {string} filename - The lycoris.cafe filename
 * @returns {Object|null} Object with title and episode, or null if parsing fails
 */
function parselycorisFilename(filename) {
  // Remove file extension
  const nameWithoutExt = filename.replace(/\.(ass|mkv|mp4)$/i, '');
  
  // Handle different lycoris.cafe filename formats
  let match;
  
  // Format 1: "lycoris.cafe_Title_Name_-_01"
  match = nameWithoutExt.match(/^lycoris\.cafe_(.+?)_-_(\d+)$/);
  if (match) {
    return {
      title: match[1].replace(/_/g, ' '),
      episode: parseInt(match[2], 10)
    };
  }
  
  // Format 2: "[lycoris.cafe] Title Name - 01"
  match = nameWithoutExt.match(/^\[lycoris\.cafe\]\s+(.+?)\s+-\s+(\d+)$/);
  if (match) {
    return {
      title: match[1],
      episode: parseInt(match[2], 10)
    };
  }
  
  // Format 3: "[lycoris.cafe] Title Name - 01 [additional info]"
  match = nameWithoutExt.match(/^\[lycoris\.cafe\]\s+(.+?)\s+-\s+(\d+)\s+\[.+\]$/);
  if (match) {
    return {
      title: match[1],
      episode: parseInt(match[2], 10)
    };
  }
  
  return null;
}

/**
 * Reverses season mapping transformation for torrent searching
 * @param {string} seasonTitle - The season title (e.g., "Sono Bisque Doll wa Koi wo Suru Season 2")
 * @param {number} episodeNumber - The episode number in the season context (e.g., 1)
 * @returns {Object} Object with originalTitle and originalEpisode for torrent searching
 */
function reverseSeasonMapping(seasonTitle, episodeNumber) {
  // Find matching season mapping by checking if seasonTitle matches any seasonTitle in mappings
  for (const [originalTitle, mapping] of Object.entries(seasonMappings)) {
    if (mapping.seasonTitle === seasonTitle) {
      // Calculate the original episode number
      const originalEpisode = (mapping.startEpisode - 1) + episodeNumber;
      return {
        originalTitle: originalTitle,
        originalEpisode: originalEpisode
      };
    }
  }
  
  // If no mapping found, return the title and episode as-is
  return {
    originalTitle: seasonTitle,
    originalEpisode: episodeNumber
  };
}

/**
 * Main function to transform lycoris.cafe filename to torrent search parameters
 * @param {string} filename - The lycoris.cafe filename
 * @returns {Object|null} Object with title and episode for torrent searching, or null if parsing fails
 */
export function transformForTorrentSearch(filename) {
  const parsed = parselycorisFilename(filename);
  if (!parsed) {
    console.error(`[ERROR] Could not parse filename: ${filename}`);
    return null;
  }
  
  const { title, episode } = parsed;
  
  // Check if this title needs reverse season mapping
  const reversed = reverseSeasonMapping(title, episode);
  
  console.log(`[INFO] Transformed "${filename}" -> Title: "${reversed.originalTitle}", Episode: ${reversed.originalEpisode}`);
  
  return {
    title: reversed.originalTitle,
    episode: reversed.originalEpisode.toString().padStart(2, '0') // Ensure 2-digit format
  };
}

/**
 * Utility function to add new season mapping
 * @param {string} originalTitle - The original anime title
 * @param {number} startEpisode - The episode number where the new season starts
 * @param {string} seasonTitle - The title used for the new season
 */
export function addSeasonMapping(originalTitle, startEpisode, seasonTitle) {
  seasonMappings[originalTitle] = {
    startEpisode: startEpisode,
    seasonTitle: seasonTitle
  };
  
  // Save updated mappings back to file
  const config = {
    "_comment": "SubsPlease Season Mapping Configuration",
    "_description": "This file maps anime titles to their season information for SubsPlease releases. When an episode number is >= startEpisode, the title will be modified to include the season name.",
    "_example": "If Sono Bisque Doll wa Koi wo Suru has startEpisode: 13 and seasonTitle: Sono Bisque Doll wa Koi wo Suru Season 2, then episode 13+ will use the seasonTitle",
    "mappings": seasonMappings
  };
  
  try {
    const mappingPath = path.resolve('./_resources/subsplease_season_mapping.json');
    fs.writeFileSync(mappingPath, JSON.stringify(config, null, 2));
    console.log(`[INFO] Added season mapping: ${originalTitle} -> ${seasonTitle} (starts at episode ${startEpisode})`);
  } catch (error) {
    console.error(`[ERROR] Could not save season mapping: ${error.message}`);
  }
}

// Example usage and testing
if (import.meta.url === `file://${process.argv[1]}`) {
  // Test the transformation
  const testFilenames = [
    "lycoris.cafe_Sono_Bisque_Doll_wa_Koi_wo_Suru_Season_2_-_01.ass",
    "[lycoris.cafe] Sono Bisque Doll wa Koi wo Suru Season 2 - 01.ass",
    "[lycoris.cafe] Sono Bisque Doll wa Koi wo Suru Season 2 - 01 [EN].ass"
  ];
  
  console.log('Testing reverse season mapping transformations:');
  testFilenames.forEach(filename => {
    const result = transformForTorrentSearch(filename);
    if (result) {
      console.log(`✓ ${filename} -> Title: "${result.title}", Episode: ${result.episode}`);
    } else {
      console.log(`✗ Failed to parse: ${filename}`);
    }
  });
}
